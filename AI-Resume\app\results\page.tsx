"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Copy,
  Download,
  Share2,
  Target,
  Zap,
  FileText,
  ArrowRight,
  Star,
  BarChart3,
} from "lucide-react"
import Link from "next/link"

export default function ResultsPage() {
  const [copiedSuggestion, setCopiedSuggestion] = useState<number | null>(null)

  const matchScore = 78
  const improvements = [
    {
      id: 1,
      priority: "High",
      category: "Keywords",
      title: "Add Missing Technical Skills",
      description: 'Include "React", "Node.js", and "TypeScript" in your skills section',
      impact: "+12 points",
      difficulty: "Easy",
      before: "Skills: JavaScript, HTML, CSS",
      after: "Skills: JavaScript, React, Node.js, TypeScript, HTML, CSS",
      implemented: false,
    },
    {
      id: 2,
      priority: "High",
      category: "Experience",
      title: "Quantify Your Achievements",
      description: "Add specific metrics to demonstrate impact",
      impact: "+8 points",
      difficulty: "Medium",
      before: "Improved website performance",
      after: "Improved website performance by 40%, reducing load time from 3.2s to 1.9s",
      implemented: false,
    },
    {
      id: 3,
      priority: "Medium",
      category: "Format",
      title: "Optimize Section Headers",
      description: "Use standard section names for better ATS parsing",
      impact: "+5 points",
      difficulty: "Easy",
      before: "Work History",
      after: "Professional Experience",
      implemented: false,
    },
  ]

  const skillGaps = [
    { skill: "React", required: true, present: false, importance: "High" },
    { skill: "Node.js", required: true, present: false, importance: "High" },
    { skill: "TypeScript", required: true, present: false, importance: "Medium" },
    { skill: "AWS", required: false, present: false, importance: "Medium" },
    { skill: "JavaScript", required: true, present: true, importance: "High" },
    { skill: "HTML/CSS", required: true, present: true, importance: "Medium" },
  ]

  const copyToClipboard = (text: string, id: number) => {
    navigator.clipboard.writeText(text)
    setCopiedSuggestion(id)
    setTimeout(() => setCopiedSuggestion(null), 2000)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800 border-red-200"
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "Low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <BarChart3 className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">ResumeAI Results</span>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Download className="w-4 h-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Match Score Overview */}
          <Card className="mb-8 border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-8">
              <div className="grid md:grid-cols-3 gap-8 items-center">
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-4">
                    <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#e5e7eb"
                        strokeWidth="2"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#3b82f6"
                        strokeWidth="2"
                        strokeDasharray={`${matchScore}, 100`}
                        className="transition-all duration-1000 ease-out"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className={`text-3xl font-bold ${getScoreColor(matchScore)}`}>{matchScore}%</span>
                    </div>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Match Score</h2>
                  <p className="text-gray-600">Good match with room for improvement</p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Keywords Match</span>
                    <span className="font-semibold">65%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Skills Alignment</span>
                    <span className="font-semibold">72%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Experience Level</span>
                    <span className="font-semibold">85%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">ATS Compatibility</span>
                    <span className="font-semibold">90%</span>
                  </div>
                </div>

                <div className="text-center">
                  <div className="bg-white rounded-lg p-6 shadow-sm">
                    <TrendingUp className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <h3 className="font-semibold text-gray-900 mb-2">Potential Score</h3>
                    <p className="text-3xl font-bold text-green-600 mb-2">93%</p>
                    <p className="text-sm text-gray-600">With our suggestions</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Content Tabs */}
          <Tabs defaultValue="improvements" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="improvements" className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Improvements
              </TabsTrigger>
              <TabsTrigger value="skills" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                Skill Analysis
              </TabsTrigger>
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Full Report
              </TabsTrigger>
            </TabsList>

            {/* Improvements Tab */}
            <TabsContent value="improvements" className="space-y-6">
              <div className="grid gap-6">
                {improvements.map((improvement) => (
                  <Card key={improvement.id} className="border-l-4 border-l-blue-500">
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <Badge className={getPriorityColor(improvement.priority)}>
                            {improvement.priority} Priority
                          </Badge>
                          <Badge variant="outline">{improvement.category}</Badge>
                          <Badge variant="outline" className="text-green-600 border-green-200">
                            {improvement.impact}
                          </Badge>
                        </div>
                        <Badge variant="secondary">{improvement.difficulty}</Badge>
                      </div>
                      <CardTitle className="text-xl">{improvement.title}</CardTitle>
                      <p className="text-gray-600">{improvement.description}</p>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-red-500" />
                            Current
                          </h4>
                          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <code className="text-sm text-gray-800">{improvement.before}</code>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            Suggested
                          </h4>
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4 relative">
                            <code className="text-sm text-gray-800">{improvement.after}</code>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 h-8 w-8 p-0"
                              onClick={() => copyToClipboard(improvement.after, improvement.id)}
                            >
                              {copiedSuggestion === improvement.id ? (
                                <CheckCircle className="w-4 h-4 text-green-600" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Skills Analysis Tab */}
            <TabsContent value="skills" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Skill Gap Analysis</CardTitle>
                  <p className="text-gray-600">Comparison between your skills and job requirements</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {skillGaps.map((skill, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div
                            className={`w-3 h-3 rounded-full ${skill.present ? "bg-green-500" : "bg-red-500"}`}
                          ></div>
                          <div>
                            <span className="font-medium">{skill.skill}</span>
                            {skill.required && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge variant={skill.importance === "High" ? "default" : "secondary"}>
                            {skill.importance}
                          </Badge>
                          <span className={`text-sm font-medium ${skill.present ? "text-green-600" : "text-red-600"}`}>
                            {skill.present ? "Present" : "Missing"}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Full Report Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Strengths</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Strong technical background in JavaScript and web development</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Relevant experience in similar roles and industries</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Well-formatted resume with clear structure</span>
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Areas for Improvement</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
                        <span>Missing key technical skills mentioned in job posting</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
                        <span>Achievements lack specific metrics and quantification</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
                        <span>Some section headers could be more ATS-friendly</span>
                      </li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/upload">
              <Button variant="outline" size="lg" className="px-8">
                Analyze Another Resume
              </Button>
            </Link>
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 px-8">
              <Star className="w-5 h-5 mr-2" />
              Upgrade for Advanced Features
            </Button>
            <Button size="lg" className="bg-green-600 hover:bg-green-700 px-8">
              Apply to Jobs
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
