// Script to create a user in Firebase Authentication
const { initializeApp } = require('firebase/app');
const { getAuth, createUserWithEmailAndPassword } = require('firebase/auth');

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAxO5AVxrHfPYlCr0BsDtxslaX-l11DsUc",
  authDomain: "gen-lang-client-0420335794.firebaseapp.com",
  databaseURL: "https://gen-lang-client-0420335794-default-rtdb.firebaseio.com",
  projectId: "gen-lang-client-0420335794",
  storageBucket: "gen-lang-client-0420335794.firebasestorage.app",
  messagingSenderId: "************",
  appId: "1:************:web:e67b84ff7a197313a8265d"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Email and password for the new user
const email = "<EMAIL>";
const password = "password123"; // Change this to a secure password

// Create the user
createUserWithEmailAndPassword(auth, email, password)
  .then((userCredential) => {
    // User created successfully
    const user = userCredential.user;
    console.log("User created successfully:", user.uid);
  })
  .catch((error) => {
    // Handle errors
    const errorCode = error.code;
    const errorMessage = error.message;
    console.error("Error creating user:", errorCode, errorMessage);
  });
