// Dashboard functionality
document.addEventListener('DOMContentLoaded', () => {
  // Check authentication
  const token = localStorage.getItem('token');
  if (!token) {
    window.location.href = 'login.html';
    return;
  }

  // Initialize dashboard
  initializeDashboard();
});

// Initialize dashboard
async function initializeDashboard() {
  // Get user info
  const user = await getUserInfo();
  if (user) {
    document.getElementById('user-name').textContent = user.name;
  }

  // Load data
  await loadData();

  // Set up navigation
  setupNavigation();
}

// Load data from API
async function loadData() {
  try {
    // Get token
    const token = localStorage.getItem('token');

    // Fetch sales data
    const salesResponse = await fetch('http://localhost:5000/api/sales', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const salesData = await salesResponse.json();
    if (salesData.success) {
      // Process sales data
      processSalesData(salesData.data);
    }

    // <PERSON><PERSON> calls data
    const callsResponse = await fetch('http://localhost:5000/api/calls', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const callsData = await callsResponse.json();
    if (callsData.success) {
      // Process calls data
      processCallsData(callsData.data);
    }

    // Fetch company data
    const companyResponse = await fetch('http://localhost:5000/api/company', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    const companyData = await companyResponse.json();
    if (companyData.success) {
      // Process company data
      processCompanyData(companyData.data);
    }
  } catch (error) {
    console.error('Error loading data:', error);
  }
}

// Process sales data
function processSalesData(data) {
  if (!data || data.length === 0) {
    // No data available
    return;
  }

  // Calculate total sales
  const totalSales = data.reduce((sum, sale) => sum + sale.amount, 0);
  document.getElementById('total-sales').textContent = `$${totalSales.toLocaleString()}`;

  // Calculate sales change
  // For demo purposes, we'll use a random percentage
  const salesChange = (Math.random() * 10).toFixed(1);
  const salesChangeElement = document.getElementById('sales-change');
  salesChangeElement.textContent = `+${salesChange}%`;

  // Populate recent sales table
  const recentSalesTable = document.getElementById('recent-sales-table');
  if (recentSalesTable) {
    recentSalesTable.innerHTML = '';

    // Get 5 most recent sales
    const recentSales = [...data]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    recentSales.forEach(sale => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${sale.product}</td>
        <td>$${sale.amount.toLocaleString()}</td>
        <td>${new Date(sale.date).toLocaleDateString()}</td>
        <td>${sale.region}</td>
      `;
      recentSalesTable.appendChild(row);
    });
  }

  // Populate sales table
  const salesTable = document.getElementById('sales-table');
  if (salesTable) {
    salesTable.innerHTML = '';

    data.forEach(sale => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${sale.product}</td>
        <td>$${sale.amount.toLocaleString()}</td>
        <td>${sale.units}</td>
        <td>${sale.region}</td>
        <td>${sale.customer}</td>
        <td>${sale.salesPerson}</td>
        <td>${new Date(sale.date).toLocaleDateString()}</td>
        <td>${sale.category}</td>
      `;
      salesTable.appendChild(row);
    });
  }

  // Create sales trend chart
  createSalesTrendChart(data);

  // Create sales by region chart
  createSalesByRegionChart(data);
}

// Process calls data
function processCallsData(data) {
  if (!data || data.length === 0) {
    // No data available
    return;
  }

  // Update total calls
  document.getElementById('total-calls').textContent = data.length.toLocaleString();

  // Calculate calls change
  // For demo purposes, we'll use a random percentage
  const callsChange = (Math.random() * 8).toFixed(1);
  const callsChangeElement = document.getElementById('calls-change');
  callsChangeElement.textContent = `+${callsChange}%`;

  // Populate recent calls table
  const recentCallsTable = document.getElementById('recent-calls-table');
  if (recentCallsTable) {
    recentCallsTable.innerHTML = '';

    // Get 5 most recent calls
    const recentCalls = [...data]
      .sort((a, b) => new Date(b.date) - new Date(a.date))
      .slice(0, 5);

    recentCalls.forEach(call => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${call.customer}</td>
        <td>${call.purpose}</td>
        <td>${call.outcome}</td>
        <td>${new Date(call.date).toLocaleDateString()}</td>
      `;
      recentCallsTable.appendChild(row);
    });
  }

  // Populate calls table
  const callsTable = document.getElementById('calls-table');
  if (callsTable) {
    callsTable.innerHTML = '';

    data.forEach(call => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${call.customer}</td>
        <td>${call.representative}</td>
        <td>${call.duration}</td>
        <td>${call.purpose}</td>
        <td>${call.outcome}</td>
        <td>${new Date(call.date).toLocaleDateString()}</td>
        <td>${call.notes || '-'}</td>
      `;
      callsTable.appendChild(row);
    });
  }
}

// Process company data
function processCompanyData(data) {
  if (!data || data.length === 0) {
    // No data available
    return;
  }

  // Update total companies
  document.getElementById('total-companies').textContent = data.length.toLocaleString();

  // Calculate companies change
  // For demo purposes, we'll use a random percentage
  const companiesChange = (Math.random() * 5).toFixed(1);
  const companiesChangeElement = document.getElementById('companies-change');
  companiesChangeElement.textContent = `+${companiesChange}%`;

  // Populate companies table
  const companiesTable = document.getElementById('companies-table');
  if (companiesTable) {
    companiesTable.innerHTML = '';

    data.forEach(company => {
      const row = document.createElement('tr');
      row.innerHTML = `
        <td>${company.name}</td>
        <td>${company.industry}</td>
        <td>${company.size}</td>
        <td>$${company.revenue.toLocaleString()}</td>
        <td>${company.location}</td>
        <td>${company.contactPerson}</td>
        <td>${company.contactEmail}</td>
        <td>${company.contactPhone}</td>
      `;
      companiesTable.appendChild(row);
    });
  }
}

// Create sales trend chart
function createSalesTrendChart(data) {
  const ctx = document.getElementById('sales-trend-chart');
  if (!ctx) return;

  // Group sales by month
  const salesByMonth = {};
  data.forEach(sale => {
    const date = new Date(sale.date);
    const monthYear = `${date.toLocaleString('default', { month: 'short' })} ${date.getFullYear()}`;

    if (!salesByMonth[monthYear]) {
      salesByMonth[monthYear] = 0;
    }
    salesByMonth[monthYear] += sale.amount;
  });

  // Sort months chronologically
  const sortedMonths = Object.keys(salesByMonth).sort((a, b) => {
    const dateA = new Date(a);
    const dateB = new Date(b);
    return dateA - dateB;
  });

  // Create chart
  new Chart(ctx, {
    type: 'line',
    data: {
      labels: sortedMonths,
      datasets: [{
        label: 'Sales Amount',
        data: sortedMonths.map(month => salesByMonth[month]),
        borderColor: '#3498db',
        backgroundColor: 'rgba(52, 152, 219, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return '$' + value.toLocaleString();
            }
          }
        }
      },
      plugins: {
        tooltip: {
          callbacks: {
            label: function(context) {
              return '$' + context.raw.toLocaleString();
            }
          }
        }
      }
    }
  });
}

// Create sales by region chart
function createSalesByRegionChart(data) {
  const ctx = document.getElementById('sales-region-chart');
  if (!ctx) return;

  // Group sales by region
  const salesByRegion = {};
  data.forEach(sale => {
    if (!salesByRegion[sale.region]) {
      salesByRegion[sale.region] = 0;
    }
    salesByRegion[sale.region] += sale.amount;
  });

  // Create chart
  new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: Object.keys(salesByRegion),
      datasets: [{
        data: Object.values(salesByRegion),
        backgroundColor: [
          '#3498db',
          '#2ecc71',
          '#e74c3c',
          '#f39c12',
          '#9b59b6'
        ],
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          callbacks: {
            label: function(context) {
              const value = context.raw;
              const total = context.dataset.data.reduce((a, b) => a + b, 0);
              const percentage = ((value / total) * 100).toFixed(1);
              return `$${value.toLocaleString()} (${percentage}%)`;
            }
          }
        }
      }
    }
  });
}

// Setup navigation
function setupNavigation() {
  const navLinks = document.querySelectorAll('.sidebar-menu a[data-page]');
  const pages = document.querySelectorAll('.page-content');
  const pageTitle = document.getElementById('page-title');

  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();

      // Get page id
      const pageId = link.getAttribute('data-page');

      // Update active link
      navLinks.forEach(navLink => navLink.classList.remove('active'));
      link.classList.add('active');

      // Show selected page
      pages.forEach(page => {
        if (page.id === `${pageId}-page`) {
          page.classList.add('active');

          // Update page title
          pageTitle.textContent = link.textContent.trim();
        } else {
          page.classList.remove('active');
        }
      });
    });
  });
}
