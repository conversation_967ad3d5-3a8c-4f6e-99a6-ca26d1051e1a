import { database } from "@/lib/firebase";
import { ref, get, query, orderByChild, startAt, endAt, equalTo, onValue, off } from "firebase/database";

export interface ServiceCall {
  id: string;
  sc_created: string;
  branch: string;
  company: string;
  rating: string;
  uid: string;
  eng_name?: string;
  eng_remarks: string;
  sc_type: string;
  is_aspcall?: boolean;
  status: string;
  callStatus?: string;
  sc_id?: string;
  inTime?: string;
  outTime?: string;
}

export interface FilterOptions {
  startDate?: string;
  endDate?: string;
  branch?: string;
  status?: string;
  callType?: string;
}

// Fetch all service calls
export const fetchServiceCalls = async (): Promise<ServiceCall[]> => {
  // Get the correct path for service calls data
  const path = await getServiceCallsPath();
  const serviceCallsRef = ref(database, path);

  console.log('Fetching service calls from:', serviceCallsRef.toString());

  try {
    const snapshot = await get(serviceCallsRef);
    console.log('Service calls snapshot exists:', snapshot.exists());

    if (snapshot.exists()) {
      const rawData = snapshot.val();
      console.log('Raw data structure:', Object.keys(rawData).length, 'records');

      if (Object.keys(rawData).length > 0) {
        console.log('Sample record keys:', Object.keys(rawData[Object.keys(rawData)[0]] || {}));
      }

      const calls: ServiceCall[] = [];

      snapshot.forEach((childSnapshot) => {
        const val = childSnapshot.val();
        // Skip if not an object or doesn't have required fields
        if (typeof val !== 'object' || val === null) {
          console.log('Skipping invalid record:', childSnapshot.key);
          return;
        }

        // Add the record with proper ID and field normalization
        calls.push({
          id: childSnapshot.key || '',
          sc_created: val.sc_created || '',
          branch: val.branch || '',
          company: val.company || '',
          rating: val.rating || '',
          uid: val.uid || '',
          eng_name: val.eng_name || val.engname || '',
          eng_remarks: val.eng_remarks || '',
          sc_type: val.sc_type || '',
          is_aspcall: val.is_aspcall || false,
          status: val.status || val.callstatus || '',
          callStatus: val.callStatus || val.callstatus || '',
          sc_id: val.sc_id || '',
          inTime: val.inTime || '',
          outTime: val.outTime || ''
        });
      });

      console.log('Processed', calls.length, 'valid service calls');
      return calls;
    } else {
      console.log('No service calls data found in Firebase');
      return [];
    }
  } catch (error) {
    console.error('Error fetching service calls:', error);
    return [];
  }
};

// Fetch service calls with filters
export const fetchFilteredServiceCalls = async (filters: FilterOptions): Promise<ServiceCall[]> => {
  let serviceCallsRef = ref(database, 'serviceCalls');
  let queryRef;

  if (filters.branch) {
    queryRef = query(serviceCallsRef, orderByChild('branch'), equalTo(filters.branch));
  } else if (filters.status) {
    // Check both status fields
    const statusField = filters.status === 'open' || filters.status === 'closed' ? 'status' : 'callStatus';
    queryRef = query(serviceCallsRef, orderByChild(statusField), equalTo(filters.status));
  } else if (filters.startDate && filters.endDate) {
    queryRef = query(
      serviceCallsRef,
      orderByChild('sc_created'),
      startAt(filters.startDate),
      endAt(filters.endDate)
    );
  } else if (filters.callType) {
    if (filters.callType === 'asp') {
      queryRef = query(serviceCallsRef, orderByChild('is_aspcall'), equalTo(true));
    } else {
      queryRef = query(serviceCallsRef, orderByChild('sc_type'), equalTo(filters.callType));
    }
  } else {
    // No filters, get all calls
    queryRef = serviceCallsRef;
  }

  const snapshot = await get(queryRef);

  if (snapshot.exists()) {
    const calls: ServiceCall[] = [];
    snapshot.forEach((childSnapshot) => {
      calls.push({
        id: childSnapshot.key || '',
        ...childSnapshot.val()
      });
    });
    return calls;
  } else {
    return [];
  }
};

// Get the correct database path for service calls
export const getServiceCallsPath = async (): Promise<string> => {
  console.log('Checking for service calls data paths...');
  // Try different possible paths for the data
  const possiblePaths = ['serviceCalls', 'ServiceCalls', 'service_calls', 'service-calls', 'calls', 'data', 'serviceCallData'];

  // Try each path until we find data
  for (const path of possiblePaths) {
    const testRef = ref(database, path);
    console.log('Checking path:', testRef.toString());
    try {
      const testSnapshot = await get(testRef);
      console.log('Path check result for', path, '- exists:', testSnapshot.exists());
      if (testSnapshot.exists()) {
        console.log('Found data at path:', path);
        console.log('Data sample:', JSON.stringify(testSnapshot.val()).substring(0, 100) + '...');
        return path;
      }
    } catch (error) {
      console.error('Error checking path:', path, error);
    }
  }

  // If we couldn't find data in any of the expected paths, try the root
  console.log('Checking root path for data structure...');
  try {
    const rootRef = ref(database, '/');
    const rootSnapshot = await get(rootRef);
    if (rootSnapshot.exists()) {
      console.log('Root data exists. Available keys:', Object.keys(rootSnapshot.val()));
      // If we find a key with 'call' in it, use that
      const callsKey = Object.keys(rootSnapshot.val()).find(key =>
        key.toLowerCase().includes('call') || key.toLowerCase().includes('service'));
      if (callsKey) {
        console.log('Found potential service calls key in root:', callsKey);
        return callsKey;
      }
    } else {
      console.log('No data found at root level');
    }
  } catch (error) {
    console.error('Error checking root path:', error);
  }

  console.log('Could not find data in any of the expected paths. Using default path: serviceCalls');
  return 'serviceCalls';
};

// Subscribe to service calls for real-time updates
export const subscribeToServiceCalls = (callback: (calls: ServiceCall[]) => void) => {
  console.log('Starting subscription to service calls...');
  // Start with default path, but try to find the correct one
  let serviceCallsRef = ref(database, 'serviceCalls');
  console.log('Initial reference path:', serviceCallsRef.toString());

  // Try to find the correct path
  getServiceCallsPath().then(path => {
    console.log('Found path for service calls:', path);
    serviceCallsRef = ref(database, path);
    console.log('Subscribing to service calls at path:', serviceCallsRef.toString());

    // Set up the subscription with the correct path
    setupSubscription(serviceCallsRef, callback);
  }).catch(error => {
    console.error('Error finding service calls path:', error);
    // Try with the default path anyway
    console.log('Falling back to default path: serviceCalls');
    setupSubscription(serviceCallsRef, callback);
  });

  // Return a function to unsubscribe
  return () => {
    console.log('Unsubscribing from service calls');
    off(serviceCallsRef);
  };
};

// Helper function to set up the subscription
const setupSubscription = (serviceCallsRef: any, callback: (calls: ServiceCall[]) => void) => {
  console.log('Setting up subscription at path:', serviceCallsRef.toString());

  const handleDataChange = (snapshot: any) => {
    console.log('Service calls data changed. Snapshot exists:', snapshot.exists());
    console.log('Snapshot key:', snapshot.key);

    if (snapshot.exists()) {
      const rawData = snapshot.val();
      console.log('Raw data structure:', Object.keys(rawData).length, 'records');
      console.log('Sample record keys:', Object.keys(rawData[Object.keys(rawData)[0]] || {}));

      const calls: ServiceCall[] = [];

      // Handle different data structures
      snapshot.forEach((childSnapshot: any) => {
        const val = childSnapshot.val();
        // Skip if not an object or doesn't have required fields
        if (typeof val !== 'object' || val === null) {
          console.log('Skipping invalid record:', childSnapshot.key);
          return;
        }

        // Add the record with proper ID
        calls.push({
          id: childSnapshot.key || '',
          sc_created: val.sc_created || '',
          branch: val.branch || '',
          company: val.company || '',
          rating: val.rating || '',
          uid: val.uid || '',
          eng_name: val.eng_name || val.engname || '',
          eng_remarks: val.eng_remarks || '',
          sc_type: val.sc_type || '',
          is_aspcall: val.is_aspcall || false,
          status: val.status || val.callstatus || '',
          callStatus: val.callStatus || val.callstatus || '',
          sc_id: val.sc_id || '',
          inTime: val.inTime || '',
          outTime: val.outTime || ''
        });
      });

      console.log('Processed', calls.length, 'valid service calls');
      callback(calls);
    } else {
      console.log('No service calls data found in Firebase');
      callback([]);
    }
  };

  onValue(serviceCallsRef, handleDataChange, (error) => {
    console.error('Error subscribing to service calls:', error);
  });
};

// Get summary metrics
export const getServiceCallMetrics = async () => {
  const calls = await fetchServiceCalls();

  // Total calls
  const totalCalls = calls.length;

  // Calls by status
  const openCalls = calls.filter(call =>
    call.status === 'open' || call.callStatus === 'open'
  ).length;

  const closedCalls = calls.filter(call =>
    call.status === 'closed' || call.callStatus === 'closed'
  ).length;

  // Calls by branch
  const callsByBranch = calls.reduce((acc: {[key: string]: number}, call) => {
    acc[call.branch] = (acc[call.branch] || 0) + 1;
    return acc;
  }, {});

  // Calls by type
  const callsByType = calls.reduce((acc: {[key: string]: number}, call) => {
    const type = call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular');
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // Calls over time (last 7 days)
  const today = new Date();
  const last7Days = Array.from({length: 7}, (_, i) => {
    const date = new Date();
    date.setDate(today.getDate() - i);
    return date.toISOString().split('T')[0];
  }).reverse();

  const callsOverTime = last7Days.map(date => {
    return {
      date,
      count: calls.filter(call => {
        const callDate = new Date(call.sc_created).toISOString().split('T')[0];
        return callDate === date;
      }).length
    };
  });

  return {
    totalCalls,
    openCalls,
    closedCalls,
    callsByBranch,
    callsByType,
    callsOverTime
  };
};
