"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Copy,
  Download,
  Share2,
  Target,
  Zap,
  FileText,
  ArrowRight,
  Star,
  BarChart3,
  ArrowLeft,
  RefreshCw,
} from "lucide-react"
import Link from "next/link"
import { useRouter, useParams } from "next/navigation"
import { useApp } from "../../providers"

export default function ResultsPage() {
  const { state, dispatch } = useApp()
  const router = useRouter()
  const params = useParams()
  const analysisId = params.id as string

  const [copiedSuggestion, setCopiedSuggestion] = useState<number | null>(null)
  const [analysis, setAnalysis] = useState<any>(null)

  useEffect(() => {
    // Redirect if not authenticated
    if (!state.user) {
      router.push("/auth/signin")
      return
    }

    // Find the analysis
    const foundAnalysis = state.analyses.find((a) => a.id === analysisId)
    if (!foundAnalysis) {
      router.push("/dashboard")
      return
    }

    if (foundAnalysis.status !== "completed") {
      router.push(`/processing/${analysisId}`)
      return
    }

    setAnalysis(foundAnalysis)
  }, [state.user, state.analyses, analysisId, router])

  const copyToClipboard = (text: string, id: number) => {
    navigator.clipboard.writeText(text)
    setCopiedSuggestion(id)
    setTimeout(() => setCopiedSuggestion(null), 2000)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High":
        return "bg-red-100 text-red-800 border-red-200"
      case "Medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "Low":
        return "bg-green-100 text-green-800 border-green-200"
      default:
        return "bg-gray-100 text-gray-800 border-gray-200"
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const handleImplementSuggestion = (suggestionId: string) => {
    if (!analysis) return

    const updatedImprovements = analysis.improvements.map((imp: any) =>
      imp.id === suggestionId ? { ...imp, implemented: !imp.implemented } : imp,
    )

    const updatedAnalysis = {
      ...analysis,
      improvements: updatedImprovements,
    }

    setAnalysis(updatedAnalysis)
    dispatch({ type: "UPDATE_ANALYSIS", payload: updatedAnalysis })
  }

  if (!state.user || !analysis) {
    return null
  }

  const implementedCount = analysis.improvements.filter((imp: any) => imp.implemented).length
  const potentialScore = analysis.matchScore + implementedCount * 5 // Rough calculation

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/dashboard" className="flex items-center space-x-2">
              <ArrowLeft className="w-5 h-5 text-gray-600" />
              <span className="text-gray-600">Back to Dashboard</span>
            </Link>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Analysis Results</span>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm">
              <Share2 className="w-4 h-4 mr-2" />
              Share
            </Button>
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              <Download className="w-4 h-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Analysis Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{analysis.fileName}</h1>
                <p className="text-gray-600">{analysis.jobTitle}</p>
                <p className="text-sm text-gray-500">Analyzed on {new Date(analysis.createdAt).toLocaleDateString()}</p>
              </div>
              <Link href="/upload">
                <Button variant="outline" className="flex items-center gap-2">
                  <RefreshCw className="w-4 h-4" />
                  New Analysis
                </Button>
              </Link>
            </div>
          </div>

          {/* Match Score Overview */}
          <Card className="mb-8 border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50">
            <CardContent className="p-8">
              <div className="grid md:grid-cols-3 gap-8 items-center">
                <div className="text-center">
                  <div className="relative w-32 h-32 mx-auto mb-4">
                    <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#e5e7eb"
                        strokeWidth="2"
                      />
                      <path
                        d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                        fill="none"
                        stroke="#3b82f6"
                        strokeWidth="2"
                        strokeDasharray={`${analysis.matchScore}, 100`}
                        className="transition-all duration-1000 ease-out"
                      />
                    </svg>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <span className={`text-3xl font-bold ${getScoreColor(analysis.matchScore)}`}>
                        {analysis.matchScore}%
                      </span>
                    </div>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Current Score</h2>
                  <p className="text-gray-600">
                    {analysis.matchScore >= 80
                      ? "Excellent match!"
                      : analysis.matchScore >= 60
                        ? "Good match with room for improvement"
                        : "Needs significant improvement"}
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Keywords Match</span>
                    <span className="font-semibold">{Math.max(0, analysis.matchScore - 15)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Skills Alignment</span>
                    <span className="font-semibold">{Math.max(0, analysis.matchScore - 8)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Experience Level</span>
                    <span className="font-semibold">{Math.min(100, analysis.matchScore + 10)}%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">ATS Compatibility</span>
                    <span className="font-semibold">{Math.min(100, analysis.matchScore + 15)}%</span>
                  </div>
                </div>

                <div className="text-center">
                  <div className="bg-white rounded-lg p-6 shadow-sm">
                    <TrendingUp className="w-12 h-12 text-green-600 mx-auto mb-4" />
                    <h3 className="font-semibold text-gray-900 mb-2">Potential Score</h3>
                    <p className="text-3xl font-bold text-green-600 mb-2">{Math.min(100, potentialScore)}%</p>
                    <p className="text-sm text-gray-600">With our suggestions</p>
                    <p className="text-xs text-gray-500 mt-2">
                      {implementedCount}/{analysis.improvements.length} implemented
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Main Content Tabs */}
          <Tabs defaultValue="improvements" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="improvements" className="flex items-center gap-2">
                <Zap className="w-4 h-4" />
                Improvements ({analysis.improvements.length})
              </TabsTrigger>
              <TabsTrigger value="skills" className="flex items-center gap-2">
                <Target className="w-4 h-4" />
                Skill Analysis
              </TabsTrigger>
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                Full Report
              </TabsTrigger>
            </TabsList>

            {/* Improvements Tab */}
            <TabsContent value="improvements" className="space-y-6">
              <div className="grid gap-6">
                {analysis.improvements.map((improvement: any) => (
                  <Card
                    key={improvement.id}
                    className={`border-l-4 ${improvement.implemented ? "border-l-green-500 bg-green-50" : "border-l-blue-500"}`}
                  >
                    <CardHeader>
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <Badge className={getPriorityColor(improvement.priority)}>
                            {improvement.priority} Priority
                          </Badge>
                          <Badge variant="outline">{improvement.category}</Badge>
                          <Badge variant="outline" className="text-green-600 border-green-200">
                            {improvement.impact}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{improvement.difficulty}</Badge>
                          <Button
                            size="sm"
                            variant={improvement.implemented ? "default" : "outline"}
                            onClick={() => handleImplementSuggestion(improvement.id)}
                            className={improvement.implemented ? "bg-green-600 hover:bg-green-700" : ""}
                          >
                            {improvement.implemented ? (
                              <>
                                <CheckCircle className="w-4 h-4 mr-1" />
                                Implemented
                              </>
                            ) : (
                              "Mark as Done"
                            )}
                          </Button>
                        </div>
                      </div>
                      <CardTitle className="text-xl">{improvement.title}</CardTitle>
                      <p className="text-gray-600">{improvement.description}</p>
                    </CardHeader>
                    <CardContent>
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                            <AlertTriangle className="w-4 h-4 text-red-500" />
                            Current
                          </h4>
                          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <code className="text-sm text-gray-800">{improvement.before}</code>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2 flex items-center gap-2">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            Suggested
                          </h4>
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4 relative">
                            <code className="text-sm text-gray-800">{improvement.after}</code>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="absolute top-2 right-2 h-8 w-8 p-0"
                              onClick={() => copyToClipboard(improvement.after, Number.parseInt(improvement.id))}
                            >
                              {copiedSuggestion === Number.parseInt(improvement.id) ? (
                                <CheckCircle className="w-4 h-4 text-green-600" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Skills Analysis Tab */}
            <TabsContent value="skills" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Skill Gap Analysis</CardTitle>
                  <p className="text-gray-600">Comparison between your skills and job requirements</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analysis.skillGaps.map((skill: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center gap-4">
                          <div
                            className={`w-3 h-3 rounded-full ${skill.present ? "bg-green-500" : "bg-red-500"}`}
                          ></div>
                          <div>
                            <span className="font-medium">{skill.skill}</span>
                            {skill.required && (
                              <Badge variant="outline" className="ml-2 text-xs">
                                Required
                              </Badge>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge variant={skill.importance === "High" ? "default" : "secondary"}>
                            {skill.importance}
                          </Badge>
                          <span className={`text-sm font-medium ${skill.present ? "text-green-600" : "text-red-600"}`}>
                            {skill.present ? "Present" : "Missing"}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Full Report Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Strengths</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Strong technical background matching job requirements</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Relevant experience in similar roles and industries</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Well-formatted resume with clear structure</span>
                      </li>
                      <li className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-green-500 mt-0.5" />
                        <span>Good use of professional language and terminology</span>
                      </li>
                    </ul>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Areas for Improvement</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-3">
                      {analysis.improvements.slice(0, 4).map((improvement: any, index: number) => (
                        <li key={index} className="flex items-start gap-3">
                          <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
                          <span>{improvement.description}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle>Detailed Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-8">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-4">ATS Compatibility</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">File Format</span>
                          <span className="text-green-600 font-medium">✓ PDF Compatible</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Font Usage</span>
                          <span className="text-green-600 font-medium">✓ Standard Fonts</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Section Headers</span>
                          <span className="text-yellow-600 font-medium">⚠ Needs Optimization</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Keyword Density</span>
                          <span className="text-yellow-600 font-medium">⚠ Could Improve</span>
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-4">Content Quality</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Quantified Achievements</span>
                          <span className="text-yellow-600 font-medium">40% of bullets</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Action Verbs</span>
                          <span className="text-green-600 font-medium">85% usage</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Relevant Keywords</span>
                          <span className="text-yellow-600 font-medium">65% match</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Length</span>
                          <span className="text-green-600 font-medium">✓ Optimal</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="mt-8 flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/upload">
              <Button variant="outline" size="lg" className="px-8">
                Analyze Another Resume
              </Button>
            </Link>
            {state.user.plan === "free" && (
              <Button size="lg" className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8">
                <Star className="w-5 h-5 mr-2" />
                Upgrade for Advanced Features
              </Button>
            )}
            <Button size="lg" className="bg-green-600 hover:bg-green-700 px-8">
              Apply to Jobs
              <ArrowRight className="w-5 h-5 ml-2" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
