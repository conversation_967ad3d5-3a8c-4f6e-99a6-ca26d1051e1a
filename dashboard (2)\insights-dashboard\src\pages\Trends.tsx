
import { useState, useEffect, useMemo } from "react";
import { CallsTimeSeries } from "@/components/dashboard/CallsTimeSeries";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { StatCard } from "@/components/dashboard/StatCard";
import { FilterOptions, ServiceCall, fetchFilteredServiceCalls, subscribeToServiceCalls } from "@/services/serviceCallService";
import { Activity, TrendingUp, TrendingDown } from "lucide-react";

const Trends = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Calls over time data
  const timeSeriesData = useMemo(() => {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const timeSeriesData = [];

    // Group calls by date
    const callsByDate = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const dateCreated = new Date(call.sc_created);
      if (dateCreated >= startOfMonth) {
        const dateKey = dateCreated.toISOString().split('T')[0];
        acc[dateKey] = (acc[dateKey] || 0) + 1;
      }
      return acc;
    }, {});

    // Fill in the time series data
    for (let d = new Date(startOfMonth); d <= now; d.setDate(d.getDate() + 1)) {
      const dateKey = d.toISOString().split('T')[0];
      timeSeriesData.push({
        date: dateKey,
        count: callsByDate[dateKey] || 0
      });
    }

    return timeSeriesData;
  }, [serviceCalls]);

  // Weekly trend calculation
  const weeklyTrend = useMemo(() => {
    if (timeSeriesData.length < 14) return { value: 0, isPositive: true };

    const currentWeekData = timeSeriesData.slice(-7);
    const previousWeekData = timeSeriesData.slice(-14, -7);

    const currentWeekTotal = currentWeekData.reduce((sum, day) => sum + day.count, 0);
    const previousWeekTotal = previousWeekData.reduce((sum, day) => sum + day.count, 0);

    if (previousWeekTotal === 0) return { value: 0, isPositive: true };

    const percentChange = ((currentWeekTotal - previousWeekTotal) / previousWeekTotal) * 100;
    return {
      value: Math.abs(Math.round(percentChange)),
      isPositive: percentChange >= 0
    };
  }, [timeSeriesData]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Fetch filtered service calls
  useEffect(() => {
    let unsubscribe: () => void;

    const setupRealtime = () => {
      unsubscribe = subscribeToServiceCalls((calls) => {
        let filteredCalls = calls;

        // Apply current filters to the real-time data
        if (filters.branch) {
          filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
        }
        if (filters.status) {
          filteredCalls = filteredCalls.filter(call =>
            call.status === filters.status || call.callStatus === filters.status
          );
        }
        if (filters.callType) {
          if (filters.callType === 'ASP') {
            filteredCalls = filteredCalls.filter(call => call.is_aspcall);
          } else {
            filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
          }
        }
        if (filters.startDate && filters.endDate) {
          filteredCalls = filteredCalls.filter(call => {
            const callDate = new Date(call.sc_created).toISOString().split('T')[0];
            return callDate >= filters.startDate! && callDate <= filters.endDate!;
          });
        }

        setServiceCalls(filteredCalls);
        setIsLoading(false);
      });
    };

    setupRealtime();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters]);

  return (
    <div>
      <div data-aos="fade-right" data-aos-duration="800">
        <h1 className="mb-6 text-2xl font-bold">Call Trends Dashboard</h1>
      </div>

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div data-aos="fade-right" data-aos-delay="100">
          <StatCard
            title="Total Calls"
            value={serviceCalls.length}
            icon={<Activity className="h-4 w-4" />}
          />
        </div>
        <div data-aos="fade-left" data-aos-delay="100">
          <StatCard
            title="Weekly Trend"
            value={`${weeklyTrend.value}%`}
            icon={weeklyTrend.isPositive ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
            trend={weeklyTrend}
          />
        </div>
      </div>

      <div className="mb-6 grid gap-4" data-aos="fade-up" data-aos-duration="1000">
        <CallsTimeSeries data={timeSeriesData} />
      </div>

      <div className="mb-6" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
        <h2 className="mb-4 text-xl font-semibold">Daily Call Volume</h2>
        <div className="rounded-lg border bg-card p-4">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="pb-2 text-left">Date</th>
                <th className="pb-2 text-right">Call Volume</th>
              </tr>
            </thead>
            <tbody>
              {timeSeriesData.slice(-14).map((day, i) => (
                <tr key={i} className={i % 2 === 0 ? "bg-muted/50" : ""}>
                  <td className="py-2">
                    {new Date(day.date).toLocaleDateString('en-US', {
                      weekday: 'short',
                      month: 'short',
                      day: 'numeric'
                    })}
                  </td>
                  <td className="py-2 text-right">{day.count}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default Trends;
