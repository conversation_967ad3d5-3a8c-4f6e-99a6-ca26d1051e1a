import pandas as pd
import firebase_admin
from firebase_admin import credentials
from firebase_admin import db
import json
import os

# This is a simplified version for smaller CSV files
# For large files, use import_csv_to_firebase.py instead

# Firebase configuration from insights-dashboard
FIREBASE_CONFIG = {
    "apiKey": "AIzaSyAE3zritBmc8cGl2UfECHjqHbMGL9JBtos",
    "authDomain": "atandra-dashboard.firebaseapp.com",
    "projectId": "atandra-dashboard",
    "databaseURL": "https://atandra-dashboard-default-rtdb.firebaseio.com", # Added database URL
    "storageBucket": "atandra-dashboard.appspot.com", # Corrected storage bucket
    "messagingSenderId": "481516888824",
    "appId": "1:481516888824:web:e9743594c42e408af0510d"
}

def main():
    print("CSV to Firebase Importer (Simple Version)")
    print("Note: This script is for smaller CSV files. For large files, use import_csv_to_firebase.py")
    print("----------------------------------------")

    # Initialize Firebase
    try:
        # Initialize the app without credentials (will use Google Application Default Credentials)
        firebase_admin.initialize_app(options={
            'databaseURL': FIREBASE_CONFIG["databaseURL"]
        })
        print("Firebase initialized successfully!")
    except Exception as e:
        print(f"Error initializing Firebase: {e}")
        print("\nAlternative method: You may need to create a service account key.")
        print("1. Go to Firebase Console > Project Settings > Service Accounts")
        print("2. Click 'Generate New Private Key'")
        print("3. Save the JSON file")

        credentials_path = input("\nEnter path to Firebase service account JSON file: ")

        try:
            cred = credentials.Certificate(credentials_path)
            firebase_admin.initialize_app(cred, {
                'databaseURL': FIREBASE_CONFIG["databaseURL"]
            })
            print("Firebase initialized successfully with service account!")
        except Exception as e2:
            print(f"Error initializing Firebase with service account: {e2}")
            return

    # Get CSV file path
    csv_path =  "ServiceCallsData.csv"

    # Get Firebase node name
    node_name = "serviceCalls"

    # Check if file exists
    if not os.path.exists(csv_path):
        print(f"Error: File {csv_path} not found")
        return

    print(f"Reading CSV file: {csv_path}")

    try:
        # Read the CSV file
        df = pd.read_csv(csv_path, encoding='latin1', low_memory=False)

        # Clean column names
        df.columns = [col.strip().replace(' ', '_').replace('.', '_').replace('-', '_').lower() for col in df.columns]

        # Convert to dictionary
        records = df.to_dict(orient='records')

        print(f"Uploading {len(records)} records to Firebase...")

        # Process in batches
        batch_size = 100
        for i in range(0, len(records), batch_size):
            batch = records[i:i+batch_size]

            # Create a batch update
            updates = {}
            for idx, record in enumerate(batch):
                # Clean the data (handle NaN, None, etc.)
                clean_record = {k: ('' if pd.isna(v) else v) for k, v in record.items()}

                # Use a unique key for each record
                record_key = f"record_{i + idx}"
                updates[record_key] = clean_record

            # Update Firebase
            db.reference(f'/{node_name}').update(updates)

            print(f"Uploaded batch {i//batch_size + 1}/{(len(records) + batch_size - 1)//batch_size}")

        print(f"Import completed! {len(records)} records imported to Firebase under '{node_name}' node.")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
