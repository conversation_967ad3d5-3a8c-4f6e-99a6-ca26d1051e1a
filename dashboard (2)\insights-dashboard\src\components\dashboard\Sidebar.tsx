
import React from "react";
import { LayoutDashboard, BarChart, <PERSON><PERSON>hart, Calendar, Settings, Users, FileText, Database, BrainCircuit, HelpCircle, Layers } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { useLocation, Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export function DashboardSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;

  const menuItems = [
    {
      title: "Dashboard",
      url: "/",
      icon: LayoutDashboard,
    },
    {
      title: "Service Calls Data",
      url: "/service-calls-data",
      icon: Database,
    },
    {
      title: "Call Trends",
      url: "/trends",
      icon: <PERSON><PERSON><PERSON>,
    },
    {
      title: "Distribution",
      url: "/distribution",
      icon: <PERSON><PERSON><PERSON>,
    },
    {
      title: "Call Calendar",
      url: "/calendar",
      icon: Calendar,
    },
    {
      title: "Engineer Reports",
      url: "/engineers",
      icon: Users,
    },
    {
      title: "Call Logs",
      url: "/logs",
      icon: FileText,
    },
    {
      title: "Settings",
      url: "/settings",
      icon: Settings,
    },
  ];

  return (
    <TooltipProvider>
      <Sidebar className="border-r border-slate-200 dark:border-slate-800 bg-white dark:bg-slate-900">
        <SidebarHeader className="pb-6">
          <div className="flex items-center gap-2 px-2">
            <div className="rounded-md bg-gradient-to-br from-blue-500 to-indigo-600 p-1.5 shadow-md">
              <BrainCircuit className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="font-semibold leading-none tracking-tight bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Atandra Insights</h3>
              <p className="text-sm text-slate-500 dark:text-slate-400">Service Call Analytics</p>
            </div>
          </div>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel className="text-xs font-medium text-slate-500 dark:text-slate-400">ANALYTICS</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {menuItems.slice(0, 4).map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton asChild className={cn(
                          "transition-all duration-200 hover:bg-slate-100 dark:hover:bg-slate-800",
                          currentPath === item.url ? "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 font-medium border-r-2 border-blue-600" : "text-slate-700 dark:text-slate-300"
                        )}>
                          <Link to={item.url} className="group">
                            <item.icon className={cn(
                              "h-5 w-5 mr-3",
                              currentPath === item.url ? "text-blue-600 dark:text-blue-400" : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-300"
                            )} />
                            <span>{item.title}</span>
                            {item.title === "Dashboard" && (
                              <Badge className="ml-auto bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900">New</Badge>
                            )}
                          </Link>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="text-xs">
                        {item.title}
                      </TooltipContent>
                    </Tooltip>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarGroup className="mt-6">
            <SidebarGroupLabel className="text-xs font-medium text-slate-500 dark:text-slate-400">MANAGEMENT</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {menuItems.slice(4).map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <SidebarMenuButton asChild className={cn(
                          "transition-all duration-200 hover:bg-slate-100 dark:hover:bg-slate-800",
                          currentPath === item.url ? "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 font-medium border-r-2 border-blue-600" : "text-slate-700 dark:text-slate-300"
                        )}>
                          <Link to={item.url} className="group">
                            <item.icon className={cn(
                              "h-5 w-5 mr-3",
                              currentPath === item.url ? "text-blue-600 dark:text-blue-400" : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-300"
                            )} />
                            <span>{item.title}</span>
                          </Link>
                        </SidebarMenuButton>
                      </TooltipTrigger>
                      <TooltipContent side="right" className="text-xs">
                        {item.title}
                      </TooltipContent>
                    </Tooltip>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>


        </SidebarContent>
        <SidebarFooter>
          <div className="flex w-full items-center justify-between p-4 text-xs text-slate-500 dark:text-slate-400 border-t border-slate-200 dark:border-slate-800">
            <div className="flex items-center">
              <HelpCircle className="h-3 w-3 mr-1" />
              <span>Help & Support</span>
            </div>
            <div>v2.1.0</div>
          </div>
        </SidebarFooter>
      </Sidebar>
    </TooltipProvider>
  );
}
