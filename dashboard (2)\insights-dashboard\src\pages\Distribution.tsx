
import { useState, useEffect, useMemo } from "react";
import { DistributionChart } from "@/components/dashboard/DistributionChart";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { StatCard } from "@/components/dashboard/StatCard";
import { FilterOptions, ServiceCall, subscribeToServiceCalls } from "@/services/serviceCallService";
import { Pie<PERSON>hart, BarChart2, Pie<PERSON><PERSON> as PieChartIcon } from "lucide-react";

const Distribution = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Calculate distribution data
  const distributionData = useMemo(() => {
    // Calls by branch
    const callsByBranch = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      acc[call.branch] = (acc[call.branch] || 0) + 1;
      return acc;
    }, {});

    const branchData = Object.entries(callsByBranch)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);

    // Calls by type
    const callsByType = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const type = call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular');
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const typeData = Object.entries(callsByType)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);

    // Calls by status
    const callsByStatus = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const status = call.status || call.callStatus || 'Unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    const statusData = Object.entries(callsByStatus)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);

    // Calls by rating
    const callsByRating = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const rating = call.rating || 'Unknown';
      acc[rating] = (acc[rating] || 0) + 1;
      return acc;
    }, {});

    const ratingData = Object.entries(callsByRating)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 8); // Top 8 ratings

    return {
      branchData,
      typeData,
      statusData,
      ratingData
    };
  }, [serviceCalls]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Set up real-time data subscription
  useEffect(() => {
    let unsubscribe: () => void;

    const setupRealtime = () => {
      unsubscribe = subscribeToServiceCalls((calls) => {
        let filteredCalls = calls;

        // Apply current filters to the real-time data
        if (filters.branch) {
          filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
        }
        if (filters.status) {
          filteredCalls = filteredCalls.filter(call =>
            call.status === filters.status || call.callStatus === filters.status
          );
        }
        if (filters.callType) {
          if (filters.callType === 'ASP') {
            filteredCalls = filteredCalls.filter(call => call.is_aspcall);
          } else {
            filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
          }
        }
        if (filters.startDate && filters.endDate) {
          filteredCalls = filteredCalls.filter(call => {
            const callDate = new Date(call.sc_created).toISOString().split('T')[0];
            return callDate >= filters.startDate! && callDate <= filters.endDate!;
          });
        }

        setServiceCalls(filteredCalls);
        setIsLoading(false);
      });
    };

    setupRealtime();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters]);

  return (
    <div>
      <div data-aos="fade-right" data-aos-duration="800">
        <h1 className="mb-6 text-2xl font-bold">Distribution Dashboard</h1>
      </div>

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <div data-aos="fade-right" data-aos-delay="100">
          <StatCard
            title="Total Locations"
            value={distributionData.branchData.length}
            icon={<PieChartIcon className="h-4 w-4" />}
          />
        </div>
        <div data-aos="fade-up" data-aos-delay="200">
          <StatCard
            title="Call Types"
            value={distributionData.typeData.length}
            icon={<BarChart2 className="h-4 w-4" />}
          />
        </div>
        <div data-aos="fade-left" data-aos-delay="100">
          <StatCard
            title="Equipment Ratings"
            value={distributionData.ratingData.length}
            icon={<PieChart className="h-4 w-4" />}
          />
        </div>
      </div>

      <div className="mb-6 grid gap-6 md:grid-cols-2">
        <div data-aos="fade-right" data-aos-duration="1000">
          <DistributionChart
            title="Calls by Branch"
            description="Distribution of service calls by location"
            data={distributionData.branchData}
            type="bar"
          />
        </div>
        <div data-aos="fade-left" data-aos-duration="1000">
          <DistributionChart
            title="Calls by Type"
            description="Distribution of service call types"
            data={distributionData.typeData}
            type="pie"
          />
        </div>
      </div>

      <div className="mb-6 grid gap-6 md:grid-cols-2">
        <div data-aos="fade-right" data-aos-duration="1000" data-aos-delay="200">
          <DistributionChart
            title="Calls by Status"
            description="Distribution of service calls by status"
            data={distributionData.statusData}
            type="pie"
          />
        </div>
        <div data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
          <DistributionChart
            title="Calls by Equipment Rating"
            description="Distribution of service calls by kVA rating"
            data={distributionData.ratingData}
            type="bar"
          />
        </div>
      </div>
    </div>
  );
};

export default Distribution;
