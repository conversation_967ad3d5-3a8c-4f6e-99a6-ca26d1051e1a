import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface RadarChartProps {
  data: {
    name: string;
    stats: {
      [key: string]: number;
    };
  }[];
  title?: string;
  description?: string;
}

export function RadarChart({ data, title, description }: RadarChartProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear previous chart
    d3.select(svgRef.current).selectAll('*').remove();

    // Set dimensions
    const width = svgRef.current.clientWidth;
    const height = svgRef.current.clientHeight;
    const margin = 60;
    const radius = Math.min(width, height) / 2 - margin;

    // Create SVG
    const svg = d3
      .select(svgRef.current)
      .attr('width', width)
      .attr('height', height)
      .append('g')
      .attr('transform', `translate(${width / 2},${height / 2})`);

    // Get all metrics (axes)
    const allMetrics = Object.keys(data[0].stats);
    const numMetrics = allMetrics.length;

    // Angle for each metric
    const angleSlice = (Math.PI * 2) / numMetrics;

    // Scale for each metric
    const maxValues = allMetrics.map(metric => 
      d3.max(data, d => d.stats[metric]) || 0
    );

    const rScale = d3.scaleLinear()
      .domain([0, d3.max(maxValues) || 0])
      .range([0, radius]);

    // Draw the circular grid
    const levels = 5;
    const gridCircles = svg.selectAll('.gridCircle')
      .data(d3.range(1, levels + 1).reverse())
      .enter()
      .append('circle')
      .attr('class', 'gridCircle')
      .attr('r', d => radius * d / levels)
      .style('fill', '#CDCDCD')
      .style('fill-opacity', 0.1)
      .style('stroke', '#CDCDCD')
      .style('stroke-width', 0.5);

    // Draw the axes
    const axes = svg.selectAll('.axis')
      .data(allMetrics)
      .enter()
      .append('g')
      .attr('class', 'axis');

    axes.append('line')
      .attr('x1', 0)
      .attr('y1', 0)
      .attr('x2', (d, i) => radius * Math.cos(angleSlice * i - Math.PI / 2))
      .attr('y2', (d, i) => radius * Math.sin(angleSlice * i - Math.PI / 2))
      .style('stroke', '#CDCDCD')
      .style('stroke-width', 1);

    // Draw the axis labels
    axes.append('text')
      .attr('class', 'legend')
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .attr('x', (d, i) => (radius + 10) * Math.cos(angleSlice * i - Math.PI / 2))
      .attr('y', (d, i) => (radius + 10) * Math.sin(angleSlice * i - Math.PI / 2))
      .text(d => d)
      .style('font-size', '10px')
      .style('fill', '#737373');

    // Draw the radar chart blobs
    const radarLine = d3.lineRadial<{ angle: number; radius: number }>()
      .curve(d3.curveLinearClosed)
      .radius(d => d.radius)
      .angle(d => d.angle);

    // Create the radar areas
    const colors = d3.schemeCategory10;

    data.forEach((d, i) => {
      const dataPoints = allMetrics.map((metric, j) => ({
        angle: angleSlice * j - Math.PI / 2,
        radius: rScale(d.stats[metric])
      }));

      // Draw the path
      svg.append('path')
        .datum(dataPoints)
        .attr('class', 'radarArea')
        .attr('d', radarLine)
        .style('fill', colors[i % colors.length])
        .style('fill-opacity', 0.3)
        .style('stroke', colors[i % colors.length])
        .style('stroke-width', 2);

      // Add dots at each data point
      svg.selectAll(`.radarCircle-${i}`)
        .data(dataPoints)
        .enter()
        .append('circle')
        .attr('class', `radarCircle-${i}`)
        .attr('r', 4)
        .attr('cx', d => d.radius * Math.cos(d.angle))
        .attr('cy', d => d.radius * Math.sin(d.angle))
        .style('fill', colors[i % colors.length])
        .style('fill-opacity', 0.8);
    });

    // Add legend
    const legendX = -width / 2 + 20;
    const legendY = -height / 2 + 20;

    const legend = svg.append('g')
      .attr('class', 'legend')
      .attr('transform', `translate(${legendX}, ${legendY})`);

    data.forEach((d, i) => {
      legend.append('rect')
        .attr('x', 0)
        .attr('y', i * 20)
        .attr('width', 10)
        .attr('height', 10)
        .style('fill', colors[i % colors.length]);

      legend.append('text')
        .attr('x', 20)
        .attr('y', i * 20 + 9)
        .text(d.name)
        .style('font-size', '10px')
        .style('fill', '#737373');
    });

  }, [data, title, description]);

  return (
    <div className="w-full h-full flex flex-col">
      {title && <h3 className="text-sm font-medium mb-2">{title}</h3>}
      {description && <p className="text-xs text-muted-foreground mb-4">{description}</p>}
      <div className="flex-1 min-h-[300px]">
        <svg ref={svgRef} width="100%" height="100%" />
      </div>
    </div>
  );
}
