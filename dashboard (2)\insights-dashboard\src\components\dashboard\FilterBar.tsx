
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Filter, X, Building, Clock, Tag, Calendar as CalendarIcon2, RefreshCw } from "lucide-react";
import { FilterOptions } from "@/services/serviceCallService";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface FilterBarProps {
  branches: string[];
  statuses: string[];
  callTypes: string[];
  onFilterChange: (filters: FilterOptions) => void;
}

export function FilterBar({
  branches,
  statuses,
  callTypes,
  onFilterChange,
}: FilterBarProps) {
  const [filters, setFilters] = useState<FilterOptions>({});
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();

  const handleFilterChange = (key: keyof FilterOptions, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handleDateChange = (type: 'start' | 'end', date?: Date) => {
    if (type === 'start') {
      setStartDate(date);
      if (date) {
        const dateString = date.toISOString().split('T')[0];
        const newFilters = { ...filters, startDate: dateString };
        if (endDate) {
          newFilters.endDate = endDate.toISOString().split('T')[0];
        }
        setFilters(newFilters);
        onFilterChange(newFilters);
      }
    } else {
      setEndDate(date);
      if (date && startDate) {
        const newFilters = {
          ...filters,
          startDate: startDate.toISOString().split('T')[0],
          endDate: date.toISOString().split('T')[0]
        };
        setFilters(newFilters);
        onFilterChange(newFilters);
      }
    }
  };

  const clearFilters = () => {
    setFilters({});
    setStartDate(undefined);
    setEndDate(undefined);
    onFilterChange({});
  };

  // Count active filters
  const activeFilterCount = Object.keys(filters).length;

  return (
    <div className="mb-6 bg-white dark:bg-slate-900 rounded-lg border border-slate-200 dark:border-slate-800 shadow-sm overflow-hidden">
      <div className="border-b border-slate-200 dark:border-slate-800 px-4 py-3 flex items-center justify-between bg-slate-50 dark:bg-slate-900">
        <div className="flex items-center">
          <Filter className="h-4 w-4 text-slate-500 dark:text-slate-400 mr-2" />
          <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300">Filter Service Calls</h3>
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300 hover:bg-blue-100">
              {activeFilterCount} active
            </Badge>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-8 px-2 text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-100"
            disabled={activeFilterCount === 0}
          >
            <RefreshCw className="h-3.5 w-3.5 mr-1" />
            <span className="text-xs">Reset</span>
          </Button>
        </div>
      </div>

      <div className="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <label className="text-xs font-medium text-slate-500 dark:text-slate-400 mb-1.5 flex items-center">
            <Building className="h-3.5 w-3.5 mr-1" /> Branch
          </label>
          <Select
            value={filters.branch}
            onValueChange={(value) => handleFilterChange("branch", value)}
          >
            <SelectTrigger className="w-full bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700">
              <SelectValue placeholder="All branches" />
            </SelectTrigger>
            <SelectContent>
              {branches.map((branch) => (
                <SelectItem key={branch} value={branch}>
                  {branch}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-xs font-medium text-slate-500 dark:text-slate-400 mb-1.5 flex items-center">
            <Clock className="h-3.5 w-3.5 mr-1" /> Status
          </label>
          <Select
            value={filters.status}
            onValueChange={(value) => handleFilterChange("status", value)}
          >
            <SelectTrigger className="w-full bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700">
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              {statuses.map((status) => (
                <SelectItem key={status} value={status}>
                  {status}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-xs font-medium text-slate-500 dark:text-slate-400 mb-1.5 flex items-center">
            <Tag className="h-3.5 w-3.5 mr-1" /> Call Type
          </label>
          <Select
            value={filters.callType}
            onValueChange={(value) => handleFilterChange("callType", value)}
          >
            <SelectTrigger className="w-full bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700">
              <SelectValue placeholder="All call types" />
            </SelectTrigger>
            <SelectContent>
              {callTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <label className="text-xs font-medium text-slate-500 dark:text-slate-400 mb-1.5 flex items-center">
            <CalendarIcon2 className="h-3.5 w-3.5 mr-1" /> Date Range
          </label>
          <div className="flex items-center gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "flex-1 justify-start text-left font-normal h-9 bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700",
                    !startDate && "text-slate-500 dark:text-slate-400"
                  )}
                >
                  <CalendarIcon className="mr-1 h-3.5 w-3.5" />
                  <span className="text-xs truncate">
                    {startDate ? format(startDate, "MMM d, yyyy") : "From"}
                  </span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={(date) => handleDateChange('start', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>

            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className={cn(
                    "flex-1 justify-start text-left font-normal h-9 bg-slate-50 dark:bg-slate-800 border-slate-200 dark:border-slate-700",
                    !endDate && "text-slate-500 dark:text-slate-400"
                  )}
                >
                  <CalendarIcon className="mr-1 h-3.5 w-3.5" />
                  <span className="text-xs truncate">
                    {endDate ? format(endDate, "MMM d, yyyy") : "To"}
                  </span>
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={(date) => handleDateChange('end', date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>

      {activeFilterCount > 0 && (
        <div className="px-4 py-2 bg-blue-50 dark:bg-blue-900/10 border-t border-blue-100 dark:border-blue-900/20 flex items-center justify-between">
          <div className="flex items-center flex-wrap gap-2">
            <span className="text-xs text-blue-700 dark:text-blue-300">Active filters:</span>
            {filters.branch && (
              <Badge variant="outline" className="text-xs bg-white dark:bg-slate-800 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 flex items-center gap-1">
                Branch: {filters.branch}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange("branch", "")} />
              </Badge>
            )}
            {filters.status && (
              <Badge variant="outline" className="text-xs bg-white dark:bg-slate-800 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 flex items-center gap-1">
                Status: {filters.status}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange("status", "")} />
              </Badge>
            )}
            {filters.callType && (
              <Badge variant="outline" className="text-xs bg-white dark:bg-slate-800 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 flex items-center gap-1">
                Type: {filters.callType}
                <X className="h-3 w-3 cursor-pointer" onClick={() => handleFilterChange("callType", "")} />
              </Badge>
            )}
            {filters.startDate && filters.endDate && (
              <Badge variant="outline" className="text-xs bg-white dark:bg-slate-800 border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-300 flex items-center gap-1">
                Date: {filters.startDate} to {filters.endDate}
                <X className="h-3 w-3 cursor-pointer" onClick={() => {
                  setStartDate(undefined);
                  setEndDate(undefined);
                  const newFilters = {...filters};
                  delete newFilters.startDate;
                  delete newFilters.endDate;
                  setFilters(newFilters);
                  onFilterChange(newFilters);
                }} />
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="h-7 px-2 text-xs text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-900/20"
          >
            Clear all
          </Button>
        </div>
      )}
    </div>
  );
}
