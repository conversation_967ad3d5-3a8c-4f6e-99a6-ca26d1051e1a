"use client"

import type React from "react"

import { create<PERSON>ontext, useContext, useReducer, type ReactNode } from "react"

// Types
interface User {
  id: string
  email: string
  name: string
  plan: "free" | "pro" | "enterprise"
  analysesUsed: number
  analysesLimit: number
}

interface Analysis {
  id: string
  fileName: string
  jobTitle: string
  matchScore: number
  createdAt: string
  status: "processing" | "completed" | "failed"
  improvements: Improvement[]
  skillGaps: SkillGap[]
}

interface Improvement {
  id: string
  priority: "High" | "Medium" | "Low"
  category: string
  title: string
  description: string
  impact: string
  difficulty: string
  before: string
  after: string
  implemented: boolean
}

interface SkillGap {
  skill: string
  required: boolean
  present: boolean
  importance: "High" | "Medium" | "Low"
}

interface AppState {
  user: User | null
  currentAnalysis: Analysis | null
  analyses: Analysis[]
  isLoading: boolean
  error: string | null
}

type AppAction =
  | { type: "SET_USER"; payload: User | null }
  | { type: "SET_CURRENT_ANALYSIS"; payload: Analysis | null }
  | { type: "ADD_ANALYSIS"; payload: Analysis }
  | { type: "UPDATE_ANALYSIS"; payload: Analysis }
  | { type: "SET_ANALYSES"; payload: Analysis[] }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: string | null }
  | { type: "CLEAR_ERROR" }

const initialState: AppState = {
  user: null,
  currentAnalysis: null,
  analyses: [],
  isLoading: false,
  error: null,
}

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case "SET_USER":
      return { ...state, user: action.payload }
    case "SET_CURRENT_ANALYSIS":
      return { ...state, currentAnalysis: action.payload }
    case "ADD_ANALYSIS":
      return { ...state, analyses: [action.payload, ...state.analyses] }
    case "UPDATE_ANALYSIS":
      return {
        ...state,
        analyses: state.analyses.map((a) => (a.id === action.payload.id ? action.payload : a)),
        currentAnalysis: state.currentAnalysis?.id === action.payload.id ? action.payload : state.currentAnalysis,
      }
    case "SET_ANALYSES":
      return { ...state, analyses: action.payload }
    case "SET_LOADING":
      return { ...state, isLoading: action.payload }
    case "SET_ERROR":
      return { ...state, error: action.payload }
    case "CLEAR_ERROR":
      return { ...state, error: null }
    default:
      return state
  }
}

const AppContext = createContext<{
  state: AppState
  dispatch: React.Dispatch<AppAction>
} | null>(null)

export function Providers({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  return <AppContext.Provider value={{ state, dispatch }}>{children}</AppContext.Provider>
}

export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error("useApp must be used within Providers")
  }
  return context
}
