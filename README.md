# Corporate Sales Dashboard

A comprehensive Node.js dashboard application for analyzing and finding trends in corporate sales data. This application provides insights into sales performance, customer calls, and company information.

## Features

- **User Authentication**: Secure login system to protect sensitive data
- **Modern UI**: Clean and responsive design for optimal user experience
- **Multiple Data Views**: 6 different tabs for various data types
  - Overview Dashboard
  - Sales Data
  - Calls Data
  - Company Data
  - Trends Analysis
  - Insights
- **Interactive Charts**: Visual representation of data using Chart.js
- **Data Filtering**: Filter data by region, date, product, etc.
- **AI Chatbot**: Interactive assistant that can answer questions about your data

## Technology Stack

- **Backend**: Node.js, Express.js, MongoDB
- **Frontend**: HTML, CSS, JavaScript, Chart.js
- **Authentication**: JWT (JSON Web Tokens)
- **Data Visualization**: Chart.js

## Installation

1. Clone the repository
   ```
   git clone https://github.com/yourusername/corporate-sales-dashboard.git
   ```

2. Install dependencies
   ```
   npm install
   ```

3. Create a `.env` file in the root directory with the following variables:
   ```
   NODE_ENV=development
   PORT=5000
   MONGO_URI=your_mongodb_connection_string
   JWT_SECRET=your_jwt_secret
   JWT_EXPIRE=30d
   ```

4. Run the application
   ```
   npm run dev
   ```

## Usage

1. Register a new user or login with existing credentials
2. Navigate through the different tabs to view and analyze data
3. Use the filters to narrow down data based on specific criteria
4. Interact with the chatbot for quick insights and answers about your data

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Chart.js for the beautiful data visualizations
- Bootstrap for the responsive UI components
