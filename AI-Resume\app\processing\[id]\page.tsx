"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle, Zap, FileText, Target, TrendingUp, Brain } from "lucide-react"
import { useRouter, useParams } from "next/navigation"
import { useApp } from "../../providers"

export default function ProcessingPage() {
  const { state, dispatch } = useApp()
  const router = useRouter()
  const params = useParams()
  const analysisId = params.id as string

  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const [processingTime, setProcessingTime] = useState(0)

  const steps = [
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Parsing Resume",
      description: "Extracting text and analyzing structure",
      duration: 2000,
    },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Analyzing Job Match",
      description: "Comparing requirements with your experience",
      duration: 3000,
    },
    {
      icon: <Brain className="w-6 h-6" />,
      title: "AI Processing",
      description: "Generating personalized insights and recommendations",
      duration: 4000,
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Creating Report",
      description: "Finalizing your detailed analysis",
      duration: 2000,
    },
  ]

  const tips = [
    "75% of resumes are rejected by ATS systems before reaching human recruiters",
    "Including relevant keywords can increase your chances by up to 40%",
    "The average recruiter spends only 6 seconds reviewing a resume",
    "Tailoring your resume for each job application improves success rates significantly",
    "Action verbs at the start of bullet points increase readability by 25%",
    "Quantified achievements are 3x more likely to catch recruiter attention",
  ]

  const [currentTip, setCurrentTip] = useState(0)

  useEffect(() => {
    // Redirect if not authenticated or no current analysis
    if (!state.user) {
      router.push("/auth/signin")
      return
    }

    const analysis = state.analyses.find((a) => a.id === analysisId)
    if (!analysis) {
      router.push("/dashboard")
      return
    }

    if (analysis.status === "completed") {
      router.push(`/results/${analysisId}`)
      return
    }
  }, [state.user, state.analyses, analysisId, router])

  // Progress simulation
  useEffect(() => {
    const totalDuration = steps.reduce((sum, step) => sum + step.duration, 0)
    let elapsed = 0

    const interval = setInterval(() => {
      elapsed += 100
      const newProgress = Math.min((elapsed / totalDuration) * 100, 100)
      setProgress(newProgress)
      setProcessingTime(elapsed)

      if (newProgress >= 100) {
        clearInterval(interval)
        // Complete the analysis
        setTimeout(() => {
          const completedAnalysis = {
            ...state.currentAnalysis!,
            status: "completed" as const,
            matchScore: Math.floor(Math.random() * 30) + 65, // Random score between 65-95
            improvements: [
              {
                id: "1",
                priority: "High" as const,
                category: "Keywords",
                title: "Add Missing Technical Skills",
                description: 'Include "React", "Node.js", and "TypeScript" in your skills section',
                impact: "+12 points",
                difficulty: "Easy",
                before: "Skills: JavaScript, HTML, CSS",
                after: "Skills: JavaScript, React, Node.js, TypeScript, HTML, CSS",
                implemented: false,
              },
              {
                id: "2",
                priority: "High" as const,
                category: "Experience",
                title: "Quantify Your Achievements",
                description: "Add specific metrics to demonstrate impact",
                impact: "+8 points",
                difficulty: "Medium",
                before: "Improved website performance",
                after: "Improved website performance by 40%, reducing load time from 3.2s to 1.9s",
                implemented: false,
              },
              {
                id: "3",
                priority: "Medium" as const,
                category: "Format",
                title: "Optimize Section Headers",
                description: "Use standard section names for better ATS parsing",
                impact: "+5 points",
                difficulty: "Easy",
                before: "Work History",
                after: "Professional Experience",
                implemented: false,
              },
            ],
            skillGaps: [
              { skill: "React", required: true, present: false, importance: "High" as const },
              { skill: "Node.js", required: true, present: false, importance: "High" as const },
              { skill: "TypeScript", required: true, present: false, importance: "Medium" as const },
              { skill: "JavaScript", required: true, present: true, importance: "High" as const },
              { skill: "HTML/CSS", required: true, present: true, importance: "Medium" as const },
            ],
          }

          dispatch({ type: "UPDATE_ANALYSIS", payload: completedAnalysis })
          router.push(`/results/${analysisId}`)
        }, 1000)
      }
    }, 100)

    return () => clearInterval(interval)
  }, [analysisId, dispatch, router, state.currentAnalysis])

  // Step progression
  useEffect(() => {
    let stepElapsed = 0
    let currentStepIndex = 0

    const stepInterval = setInterval(() => {
      stepElapsed += 100

      if (currentStepIndex < steps.length - 1 && stepElapsed >= steps[currentStepIndex].duration) {
        currentStepIndex++
        setCurrentStep(currentStepIndex)
        stepElapsed = 0
      }
    }, 100)

    return () => clearInterval(stepInterval)
  }, [])

  // Tip rotation
  useEffect(() => {
    const tipInterval = setInterval(() => {
      setCurrentTip((prev) => (prev + 1) % tips.length)
    }, 3000)

    return () => clearInterval(tipInterval)
  }, [tips.length])

  if (!state.user || !state.currentAnalysis) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          {/* Progress Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-white animate-pulse" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Analyzing Your Resume</h1>
            <p className="text-lg text-gray-600">Our AI is working hard to provide you with personalized insights</p>
            <div className="mt-4 text-sm text-gray-500">Processing time: {Math.floor(processingTime / 1000)}s</div>
          </div>

          {/* Progress Bar */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Analysis Progress</span>
                  <span className="text-sm font-medium text-blue-600">{Math.round(progress)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>

              <div className="text-center">
                <p className="text-gray-600">
                  Estimated time remaining: {Math.max(0, Math.ceil((100 - progress) / 8))} seconds
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Processing Steps */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="space-y-4">
                {steps.map((step, index) => (
                  <div
                    key={index}
                    className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-500 ${
                      index < currentStep
                        ? "bg-green-50 border border-green-200"
                        : index === currentStep
                          ? "bg-blue-50 border border-blue-200"
                          : "bg-gray-50 border border-gray-200"
                    }`}
                  >
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                        index < currentStep
                          ? "bg-green-600 text-white"
                          : index === currentStep
                            ? "bg-blue-600 text-white"
                            : "bg-gray-300 text-gray-600"
                      }`}
                    >
                      {index < currentStep ? <CheckCircle className="w-6 h-6" /> : step.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className={`font-medium ${index <= currentStep ? "text-gray-900" : "text-gray-500"}`}>
                        {step.title}
                      </h3>
                      <p className={`text-sm ${index <= currentStep ? "text-gray-600" : "text-gray-400"}`}>
                        {step.description}
                      </p>
                    </div>
                    {index === currentStep && (
                      <div className="flex-shrink-0">
                        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tips Section */}
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">💡 Did you know?</h3>
              <div className="min-h-[60px] flex items-center">
                <p className="text-gray-600 transition-opacity duration-500">{tips[currentTip]}</p>
              </div>
              <div className="flex justify-center mt-4 space-x-2">
                {tips.map((_, index) => (
                  <div
                    key={index}
                    className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                      index === currentTip ? "bg-blue-600" : "bg-gray-300"
                    }`}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
