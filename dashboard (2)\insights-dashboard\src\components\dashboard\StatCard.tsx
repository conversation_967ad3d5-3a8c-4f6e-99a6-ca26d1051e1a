
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { ArrowUpRight, ArrowDownRight, TrendingUp, TrendingDown } from "lucide-react";

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export function StatCard({
  title,
  value,
  description,
  icon,
  className,
  trend,
}: StatCardProps) {
  return (
    <Card className={cn(
      "overflow-hidden transition-all duration-300 hover:shadow-md",
      className,
      {
        "bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-900/30": title === "Total Calls",
        "bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 border-orange-100 dark:border-orange-900/30": title === "Open Calls",
        "bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-100 dark:border-green-900/30": title === "Closed Calls",
        "bg-gradient-to-br from-blue-50 to-sky-50 dark:from-blue-900/20 dark:to-sky-900/20 border-blue-100 dark:border-blue-900/30": title === "In Progress",
      }
    )}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className={cn(
          "text-sm font-medium",
          {
            "text-blue-700 dark:text-blue-300": title === "Total Calls",
            "text-orange-700 dark:text-orange-300": title === "Open Calls",
            "text-green-700 dark:text-green-300": title === "Closed Calls",
            "text-blue-700 dark:text-blue-300": title === "In Progress",
          }
        )}>{title}</CardTitle>
        {icon && (
          <div className={cn(
            "h-8 w-8 rounded-full flex items-center justify-center",
            {
              "bg-blue-100 text-blue-600 dark:bg-blue-900/40 dark:text-blue-300": title === "Total Calls",
              "bg-orange-100 text-orange-600 dark:bg-orange-900/40 dark:text-orange-300": title === "Open Calls",
              "bg-green-100 text-green-600 dark:bg-green-900/40 dark:text-green-300": title === "Closed Calls",
              "bg-blue-100 text-blue-600 dark:bg-blue-900/40 dark:text-blue-300": title === "In Progress",
            }
          )}>
            {icon}
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className={cn(
          "text-3xl font-bold",
          {
            "text-slate-800 dark:text-slate-100": title === "Total Calls",
            "text-orange-800 dark:text-orange-100": title === "Open Calls",
            "text-green-800 dark:text-green-100": title === "Closed Calls",
            "text-blue-800 dark:text-blue-100": title === "In Progress",
          }
        )}>{value}</div>
        {description && (
          <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">{description}</p>
        )}
        {trend && (
          <div
            className={cn(
              "mt-3 flex items-center text-xs rounded-full py-1 px-2 w-fit",
              trend.isPositive
                ? "text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/30"
                : "text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900/30"
            )}
          >
            {trend.isPositive ? (
              <TrendingUp className="mr-1 h-3 w-3" />
            ) : (
              <TrendingDown className="mr-1 h-3 w-3" />
            )}
            {Math.abs(trend.value)}%
            <span className="ml-1 text-slate-600 dark:text-slate-400">from last period</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
