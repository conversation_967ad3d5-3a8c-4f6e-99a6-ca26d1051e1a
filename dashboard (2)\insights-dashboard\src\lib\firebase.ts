
import { initializeApp } from "firebase/app";
import { getDatabase } from "firebase/database";
import { getAuth } from "firebase/auth";

// For development purposes - use fallback values if environment variables aren't available
// In production, these should be set properly as environment variables with VITE_ prefix
const firebaseConfig = {
  apiKey: "AIzaSyAE3zritBmc8cGl2UfECHjqHbMGL9JBtos",
  authDomain: "atandra-dashboard.firebaseapp.com",
  projectId: "atandra-dashboard",
  databaseURL: "https://atandra-dashboard-default-rtdb.firebaseio.com", // Added database URL
  storageBucket: "atandra-dashboard.appspot.com", // Corrected storage bucket
  messagingSenderId: "481516888824",
  appId: "1:481516888824:web:e9743594c42e408af0510d"
};

console.log('Firebase config:', firebaseConfig);

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const database = getDatabase(app);
const auth = getAuth(app);

export { app, database, auth };
