import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Loader2, Lock, Mail, BrainCircuit, BarChart3, LineChart, Network } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ThemeToggle } from '@/components/ThemeToggle';
import atandraLogo from '@/assets/LOGO-at.png';

const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function Login() {
  const { signIn, error } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: LoginFormValues) => {
    setIsLoading(true);
    try {
      await signIn(data.email, data.password);
      navigate('/');
    } catch (error) {
      // Error is handled by the auth context
    } finally {
      setIsLoading(false);
    }
  };

  // Animation for floating icons
  const [animationStarted, setAnimationStarted] = useState(false);

  useEffect(() => {
    setAnimationStarted(true);
  }, []);

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 dark:from-black dark:via-slate-900 dark:to-black">
      {/* Animated background elements */}
      <div className="absolute inset-0 z-0 overflow-hidden">
        {/* Grid pattern */}
        <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxkZWZzPjxwYXR0ZXJuIGlkPSJncmlkIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiPjxwYXRoIGQ9Ik0gNDAgMCBMIDAgMCAwIDQwIiBmaWxsPSJub25lIiBzdHJva2U9InJnYmEoMjU1LDI1NSwyNTUsMC4wNSkiIHN0cm9rZS13aWR0aD0iMSIvPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgZmlsbD0idXJsKCNncmlkKSIgd2lkdGg9IjEwMCUiIGhlaWdodD0iMTAwJSIvPjwvc3ZnPg==')] opacity-20"></div>

        {/* Glowing orbs */}
        <div className="absolute top-1/4 left-1/4 h-40 w-40 -translate-x-1/2 -translate-y-1/2 rounded-full bg-blue-500 opacity-20 animate-pulse-glow"></div>
        <div className="absolute top-3/4 right-1/4 h-60 w-60 -translate-x-1/2 -translate-y-1/2 rounded-full bg-indigo-600 opacity-10 animate-pulse-glow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute bottom-1/4 left-2/3 h-32 w-32 -translate-x-1/2 -translate-y-1/2 rounded-full bg-purple-500 opacity-15 animate-pulse-glow" style={{ animationDelay: '2s' }}></div>

        {/* Rotating circuit pattern */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] opacity-5 animate-rotate-slow pointer-events-none">
          <svg viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="400" cy="400" r="350" stroke="url(#grad1)" strokeWidth="1" />
            <circle cx="400" cy="400" r="300" stroke="url(#grad1)" strokeWidth="1" />
            <circle cx="400" cy="400" r="250" stroke="url(#grad1)" strokeWidth="1" />
            <circle cx="400" cy="400" r="200" stroke="url(#grad1)" strokeWidth="1" />
            <circle cx="400" cy="400" r="150" stroke="url(#grad1)" strokeWidth="1" />
            <path d="M400 50L400 750" stroke="url(#grad1)" strokeWidth="1" />
            <path d="M50 400L750 400" stroke="url(#grad1)" strokeWidth="1" />
            <path d="M170 170L630 630" stroke="url(#grad1)" strokeWidth="1" />
            <path d="M630 170L170 630" stroke="url(#grad1)" strokeWidth="1" />
            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#3b82f6" />
                <stop offset="100%" stopColor="#8b5cf6" />
              </linearGradient>
            </defs>
          </svg>
        </div>

        {/* Floating icons with animation */}
        <div className={`absolute transition-all duration-1000 ease-out ${animationStarted ? 'opacity-30 translate-y-0' : 'opacity-0 translate-y-10'}`} style={{ top: '15%', left: '10%' }}>
          <BrainCircuit size={48} className="text-blue-300 animate-float" style={{ animationDelay: '0.2s' }} />
        </div>
        <div className={`absolute transition-all duration-1000 ease-out ${animationStarted ? 'opacity-30 translate-y-0' : 'opacity-0 translate-y-10'}`} style={{ top: '25%', right: '15%' }}>
          <BarChart3 size={64} className="text-indigo-300 animate-float" style={{ animationDelay: '1.5s' }} />
        </div>
        <div className={`absolute transition-all duration-1000 ease-out ${animationStarted ? 'opacity-30 translate-y-0' : 'opacity-0 translate-y-10'}`} style={{ bottom: '20%', left: '20%' }}>
          <LineChart size={56} className="text-cyan-300 animate-float" style={{ animationDelay: '1s' }} />
        </div>
        <div className={`absolute transition-all duration-1000 ease-out ${animationStarted ? 'opacity-30 translate-y-0' : 'opacity-0 translate-y-10'}`} style={{ bottom: '30%', right: '10%' }}>
          <Network size={72} className="text-purple-300 animate-float" style={{ animationDelay: '0.5s' }} />
        </div>
      </div>

      {/* Theme toggle */}
      <div className="absolute top-4 right-4 z-20">
        <ThemeToggle />
      </div>

      {/* Main content */}
      <div className="relative z-10 flex min-h-screen flex-col items-center justify-center p-4">
        <div className={`w-full max-w-md space-y-6 transition-all duration-1000 ease-out ${animationStarted ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Logo and title */}
          <div className="flex flex-col items-center space-y-4 text-center">
            <div className="relative w-full max-w-xs mb-2">
              <img src={atandraLogo} alt="Atandra Krykard Logo" className="w-full h-auto" />
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 to-indigo-600/0 blur-md opacity-30"></div>
            </div>
            <div>
              <h1 className="text-4xl font-bold tracking-tight text-white">Atandra <span className="bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">Insights</span></h1>
              <p className="mt-2 text-lg text-blue-200">Advanced Analytics Dashboard</p>
            </div>
          </div>

          {/* Login card with advanced glassmorphism effect */}
          <Card className="relative overflow-hidden border-0 bg-white/5 backdrop-blur-lg shadow-xl">
            {/* Card glow effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20 opacity-30"></div>
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 opacity-20 blur-sm rounded-xl"></div>
            <div className="absolute -bottom-4 -right-4 h-32 w-32 bg-blue-500 opacity-20 blur-xl rounded-full"></div>
            <div className="absolute -top-4 -left-4 h-24 w-24 bg-indigo-600 opacity-20 blur-xl rounded-full"></div>

            {/* Card header with futuristic design */}
            <CardHeader className="relative space-y-1 pb-4 border-b border-white/10">
              <div className="absolute top-0 right-0 h-1 w-16 bg-gradient-to-r from-blue-400 to-indigo-500"></div>
              <CardTitle className="text-xl font-bold text-white flex items-center">
                <div className="mr-2 h-5 w-1 bg-blue-400"></div>
                Secure Access Portal
              </CardTitle>
              <CardDescription className="text-blue-200 pl-3">
                Enter your credentials to access the dashboard
              </CardDescription>
            </CardHeader>
            <CardContent className="relative pb-6">
              {error && (
                <Alert variant="destructive" className="mb-6 border-0 bg-red-900/60 text-white backdrop-blur-sm">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Authentication Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="text-sm font-medium text-blue-100">Email Address</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute left-3 top-2.5 h-5 w-5 text-blue-300" />
                            <Input
                              placeholder="<EMAIL>"
                              type="email"
                              className="border-blue-500/30 bg-white/10 pl-10 text-white placeholder:text-blue-200/60 focus:border-blue-400 focus:ring-blue-400/50"
                              {...field}
                              disabled={isLoading}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-300" />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="text-sm font-medium text-blue-100">Password</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Lock className="absolute left-3 top-2.5 h-5 w-5 text-blue-300" />
                            <Input
                              placeholder="••••••••"
                              type="password"
                              className="border-blue-500/30 bg-white/10 pl-10 text-white placeholder:text-blue-200/60 focus:border-blue-400 focus:ring-blue-400/50"
                              {...field}
                              disabled={isLoading}
                            />
                          </div>
                        </FormControl>
                        <FormMessage className="text-red-300" />
                      </FormItem>
                    )}
                  />
                  <Button
                    type="submit"
                    className="relative mt-4 w-full overflow-hidden bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 focus:ring-blue-500 shadow-lg group"
                    disabled={isLoading}
                  >
                    {/* Button glow effect */}
                    <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-400 to-indigo-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></span>
                    <span className="absolute -inset-x-1 bottom-0 h-0.5 bg-gradient-to-r from-blue-300 to-indigo-300 opacity-50"></span>

                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        <span>Authenticating...</span>
                      </>
                    ) : (
                      <>
                        <span className="relative z-10 flex items-center justify-center">
                          <span className="mr-2 h-4 w-0.5 bg-blue-300 opacity-70"></span>
                          <span>Access Dashboard</span>
                        </span>
                      </>
                    )}
                  </Button>
                </form>
              </Form>
            </CardContent>
            <CardFooter className="relative flex justify-center border-t border-white/10 p-4">
              <div className="flex flex-col items-center space-y-2">
                <div className="flex items-center space-x-2 text-sm text-blue-200">
                  <Lock className="h-4 w-4" />
                  <p>Enterprise-grade security</p>
                </div>
                <div className="flex items-center space-x-1 text-xs text-blue-200/60">
                  <div className="h-1 w-1 rounded-full bg-green-400 animate-pulse"></div>
                  <p>Secured with advanced encryption</p>
                </div>
              </div>
            </CardFooter>
          </Card>

          {/* Footer */}
          <div className="text-center text-sm text-blue-200/80">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-blue-300/30 to-transparent"></div>
              <div className="text-blue-300/50 text-xs">measure. protect. conserve</div>
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-blue-300/30 to-transparent"></div>
            </div>
            <p>© 2025 Atandra Energy Private Limited. All rights reserved.</p>
            <p className="mt-1">Powered by CAP Corporate AI Solutions LLP</p>
            <div className="mt-3 flex items-center justify-center space-x-4 text-xs text-blue-200/60">
              <span>ISO 9001:2015 certified factory</span>
              <span className="h-1 w-1 rounded-full bg-blue-400/50"></span>
              <span>GDPR Compliant</span>
              <span className="h-1 w-1 rounded-full bg-blue-400/50"></span>
              <span>SOC 2 Type II</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
