"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle, Zap, FileText, Target, TrendingUp } from "lucide-react"
import { useRouter } from "next/navigation"

export default function ProcessingPage() {
  const [progress, setProgress] = useState(0)
  const [currentStep, setCurrentStep] = useState(0)
  const router = useRouter()

  const steps = [
    { icon: <FileText className="w-6 h-6" />, title: "Parsing Resume", description: "Extracting text and formatting" },
    {
      icon: <Target className="w-6 h-6" />,
      title: "Analyzing Job Match",
      description: "Comparing requirements and skills",
    },
    { icon: <Zap className="w-6 h-6" />, title: "AI Processing", description: "Generating personalized insights" },
    { icon: <TrendingUp className="w-6 h-6" />, title: "Creating Report", description: "Preparing your results" },
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setTimeout(() => {
            router.push("/results")
          }, 1000)
          return 100
        }
        return prev + 2
      })
    }, 100)

    return () => clearInterval(interval)
  }, [router])

  useEffect(() => {
    const stepInterval = setInterval(() => {
      setCurrentStep((prev) => {
        if (prev < steps.length - 1) {
          return prev + 1
        }
        return prev
      })
    }, 2000)

    return () => clearInterval(stepInterval)
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center">
      <div className="container mx-auto px-4">
        <div className="max-w-2xl mx-auto">
          {/* Progress Header */}
          <div className="text-center mb-8">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-white animate-pulse" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Analyzing Your Resume</h1>
            <p className="text-lg text-gray-600">Our AI is working hard to provide you with personalized insights</p>
          </div>

          {/* Progress Bar */}
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="mb-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">Progress</span>
                  <span className="text-sm font-medium text-blue-600">{progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-blue-600 to-purple-600 h-3 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${progress}%` }}
                  ></div>
                </div>
              </div>

              <div className="text-center">
                <p className="text-gray-600">
                  Estimated time remaining: {Math.max(0, Math.ceil((100 - progress) / 10))} seconds
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Processing Steps */}
          <Card>
            <CardContent className="p-6">
              <div className="space-y-4">
                {steps.map((step, index) => (
                  <div
                    key={index}
                    className={`flex items-center space-x-4 p-4 rounded-lg transition-all duration-500 ${
                      index < currentStep
                        ? "bg-green-50 border border-green-200"
                        : index === currentStep
                          ? "bg-blue-50 border border-blue-200"
                          : "bg-gray-50 border border-gray-200"
                    }`}
                  >
                    <div
                      className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                        index < currentStep
                          ? "bg-green-600 text-white"
                          : index === currentStep
                            ? "bg-blue-600 text-white"
                            : "bg-gray-300 text-gray-600"
                      }`}
                    >
                      {index < currentStep ? <CheckCircle className="w-6 h-6" /> : step.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className={`font-medium ${index <= currentStep ? "text-gray-900" : "text-gray-500"}`}>
                        {step.title}
                      </h3>
                      <p className={`text-sm ${index <= currentStep ? "text-gray-600" : "text-gray-400"}`}>
                        {step.description}
                      </p>
                    </div>
                    {index === currentStep && (
                      <div className="flex-shrink-0">
                        <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Tips Section */}
          <Card className="mt-8">
            <CardContent className="p-6">
              <h3 className="font-semibold text-gray-900 mb-4">💡 Did you know?</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <p>• 75% of resumes are rejected by ATS systems before reaching human recruiters</p>
                <p>• Including relevant keywords can increase your chances by up to 40%</p>
                <p>• The average recruiter spends only 6 seconds reviewing a resume</p>
                <p>• Tailoring your resume for each job application improves success rates significantly</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
