
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import DashboardLayout from "./pages/DashboardLayout";
import Index from "./pages/Index";
import Trends from "./pages/Trends";
import Distribution from "./pages/Distribution";
import Calendar from "./pages/Calendar";
import Engineers from "./pages/Engineers";
import Logs from "./pages/Logs";
import Settings from "./pages/Settings";
import ServiceCallsData from "./pages/ServiceCallsData";
import SearchResults from "./pages/SearchResults";
import NotFound from "./pages/NotFound";
import Login from "./pages/Login";
import ProtectedRoute from "./components/auth/ProtectedRoute";
import { AuthProvider } from "./contexts/AuthContext";
import { ThemeProvider } from "./contexts/ThemeContext";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              {/* Login is the landing page */}
              <Route path="/login" element={<Login />} />

              {/* Protected dashboard routes */}
              <Route element={<ProtectedRoute />}>
                <Route path="/" element={<DashboardLayout />}>
                  <Route index element={<Index />} />
                  <Route path="trends" element={<Trends />} />
                  <Route path="distribution" element={<Distribution />} />
                  <Route path="calendar" element={<Calendar />} />
                  <Route path="engineers" element={<Engineers />} />
                  <Route path="logs" element={<Logs />} />
                  <Route path="settings" element={<Settings />} />
                  <Route path="service-calls-data" element={<ServiceCallsData />} />
                  <Route path="search" element={<SearchResults />} />
                </Route>
              </Route>

              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
