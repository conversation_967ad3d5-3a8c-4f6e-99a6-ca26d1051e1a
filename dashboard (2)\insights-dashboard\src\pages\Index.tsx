
import { useState, useEffect, useMemo } from "react";
import { StatCard } from "@/components/dashboard/StatCard";
import { CallsTimeSeries } from "@/components/dashboard/CallsTimeSeries";
import { DistributionChart } from "@/components/dashboard/DistributionChart";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { CallTable } from "@/components/dashboard/CallTable";
import { FilterOptions, ServiceCall, subscribeToServiceCalls } from "@/services/serviceCallService";
import { Activity, Clock, CheckCircle, AlertCircle } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { database } from "@/lib/firebase";

const Index = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [error, setError] = useState<string | null>(null);
  const { currentUser, loading: authLoading } = useAuth();

  // Log authentication state
  useEffect(() => {
    console.log('Auth state:', { currentUser, authLoading });
    if (!authLoading && !currentUser) {
      console.log('User is not authenticated');
    }
  }, [currentUser, authLoading]);

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Calculate metrics
  const metrics = useMemo(() => {
    // Total calls
    const totalCalls = serviceCalls.length;

    // Calls by status
    const openCalls = serviceCalls.filter(call =>
      (call.status === 'open' || call.callStatus === 'open')
    ).length;

    const closedCalls = serviceCalls.filter(call =>
      (call.status === 'closed' || call.callStatus === 'closed')
    ).length;

    const inProgressCalls = totalCalls - openCalls - closedCalls;

    // Calls by branch
    const callsByBranch = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      acc[call.branch] = (acc[call.branch] || 0) + 1;
      return acc;
    }, {});

    const branchData = Object.entries(callsByBranch).map(([name, value]) => ({ name, value }));

    // Calls by type
    const callsByType = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const type = call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular');
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const typeData = Object.entries(callsByType).map(([name, value]) => ({ name, value }));

    // Calls by rating
    const callsByRating = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const rating = call.rating || 'Unknown';
      acc[rating] = (acc[rating] || 0) + 1;
      return acc;
    }, {});

    const ratingData = Object.entries(callsByRating)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 8); // Top 8 ratings

    // Calls over time
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const timeSeriesData = [];

    // Group calls by date
    const callsByDate = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const dateCreated = new Date(call.sc_created);
      if (dateCreated >= startOfMonth) {
        const dateKey = dateCreated.toISOString().split('T')[0];
        acc[dateKey] = (acc[dateKey] || 0) + 1;
      }
      return acc;
    }, {});

    // Fill in the time series data
    for (let d = new Date(startOfMonth); d <= now; d.setDate(d.getDate() + 1)) {
      const dateKey = d.toISOString().split('T')[0];
      timeSeriesData.push({
        date: dateKey,
        count: callsByDate[dateKey] || 0
      });
    }

    return {
      totalCalls,
      openCalls,
      closedCalls,
      inProgressCalls,
      branchData,
      typeData,
      ratingData,
      timeSeriesData
    };
  }, [serviceCalls]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Initial data load and real-time subscription
  useEffect(() => {
    let unsubscribe: () => void;
    setError(null);

    // Only proceed if authenticated or authentication is still loading
    if (authLoading || currentUser) {
      console.log('Setting up real-time data subscription...');

      const setupRealtime = () => {
        try {
          console.log('Database reference:', database);
          unsubscribe = subscribeToServiceCalls((calls) => {
            console.log('Received service calls data:', calls.length, 'records');

            if (calls.length === 0) {
              console.log('No service calls data received');
              setError('No service calls data found. Please check your database connection.');
            }

            let filteredCalls = calls;

            // Apply current filters to the real-time data
            if (filters.branch) {
              filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
            }
            if (filters.status) {
              filteredCalls = filteredCalls.filter(call =>
                call.status === filters.status || call.callStatus === filters.status
              );
            }
            if (filters.callType) {
              if (filters.callType === 'ASP') {
                filteredCalls = filteredCalls.filter(call => call.is_aspcall);
              } else {
                filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
              }
            }
            if (filters.startDate && filters.endDate) {
              filteredCalls = filteredCalls.filter(call => {
                const callDate = new Date(call.sc_created).toISOString().split('T')[0];
                return callDate >= filters.startDate! && callDate <= filters.endDate!;
              });
            }

            console.log('After filtering:', filteredCalls.length, 'records');
            setServiceCalls(filteredCalls);
            setIsLoading(false);
          });
        } catch (err) {
          console.error('Error setting up real-time subscription:', err);
          setError('Failed to connect to the database. Please check your connection.');
          setIsLoading(false);
        }
      };

      setupRealtime();
    } else {
      console.log('Not authenticated, skipping data subscription');
      setError('Please log in to view service calls data.');
      setIsLoading(false);
    }

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters, currentUser, authLoading]);

  return (
    <div>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6 gap-4">
        <div data-aos="fade-right" data-aos-duration="800">
          <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100">Service Call Dashboard</h1>
          <p className="text-slate-500 dark:text-slate-400 mt-1">Overview of all service call activities and metrics</p>
        </div>
        <div data-aos="fade-left" data-aos-duration="800" className="flex items-center space-x-2">
          <Button variant="outline" size="sm" className="text-slate-600 dark:text-slate-300 border-slate-300 dark:border-slate-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export Data
          </Button>
          <Button variant="outline" size="sm" className="text-slate-600 dark:text-slate-300 border-slate-300 dark:border-slate-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3" />
            </svg>
            Download Report
          </Button>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center h-40 bg-white dark:bg-slate-900 rounded-lg shadow-sm border border-slate-200 dark:border-slate-800">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          <p className="ml-4 text-lg text-slate-600 dark:text-slate-300">Loading service calls data...</p>
        </div>
      ) : error ? (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg shadow-sm mb-6" role="alert">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className="font-medium">Error Loading Data</p>
          </div>
          <p className="mt-2 ml-7">{error}</p>
          <p className="mt-2 ml-7 text-sm">Please check the browser console for more details.</p>
        </div>
      ) : null}

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div data-aos="fade-right" data-aos-delay="100">
          <StatCard
            title="Total Calls"
            value={metrics.totalCalls}
            icon={<Activity className="h-4 w-4" />}
          />
        </div>
        <div data-aos="fade-right" data-aos-delay="200">
          <StatCard
            title="Open Calls"
            value={metrics.openCalls}
            icon={<Clock className="h-4 w-4" />}
            className="border-l-4 border-orange-500"
          />
        </div>
        <div data-aos="fade-left" data-aos-delay="200">
          <StatCard
            title="Closed Calls"
            value={metrics.closedCalls}
            icon={<CheckCircle className="h-4 w-4" />}
            className="border-l-4 border-green-500"
          />
        </div>
        <div data-aos="fade-left" data-aos-delay="100">
          <StatCard
            title="In Progress"
            value={metrics.inProgressCalls}
            icon={<AlertCircle className="h-4 w-4" />}
            className="border-l-4 border-blue-500"
          />
        </div>
      </div>

      <div className="mb-6" data-aos="zoom-in" data-aos-duration="1000">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-blue-100 dark:border-blue-900/30 overflow-hidden shadow-sm hover:shadow-md transition-all duration-300">
          <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400/10 dark:bg-blue-400/5 rounded-full -mt-10 -mr-10"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-indigo-400/10 dark:bg-indigo-400/5 rounded-full -mb-8 -ml-8"></div>

          <CardHeader className="flex flex-row items-center justify-between pb-2 relative">
            <div>
              <div className="h-1 w-10 bg-gradient-to-r from-blue-500 to-indigo-600 mb-2"></div>
              <CardTitle className="text-lg text-slate-800 dark:text-slate-100">Imported Service Calls Data</CardTitle>
            </div>
            <Button variant="default" size="sm" asChild className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-none shadow-sm">
              <Link to="/service-calls-data">View Detailed Analysis</Link>
            </Button>
          </CardHeader>
          <CardContent className="relative">
            <p className="text-slate-600 dark:text-slate-300 mb-4">
              View detailed analysis of the imported service calls data with interactive charts and tables.
            </p>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center text-blue-600 dark:text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <span>Interactive Charts</span>
              </div>
              <div className="flex items-center text-blue-600 dark:text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
                </svg>
                <span>Advanced Filtering</span>
              </div>
              <div className="flex items-center text-blue-600 dark:text-blue-400">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>Export Options</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
        <div data-aos="fade-right" data-aos-duration="1000" className="lg:col-span-2">
          <Card className="border-slate-200 dark:border-slate-800 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 to-indigo-600"></div>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-md bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <CardTitle className="text-lg text-slate-800 dark:text-slate-100">Call Volume Trend</CardTitle>
            </div>
            <Button variant="outline" size="sm" asChild className="border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800">
              <Link to="/trends" className="flex items-center">
                <span>View Trends</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="h-[350px] pt-4">
              <div className="flex flex-col h-full">
                <div className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-2">Number of service calls over time</div>
                <div className="flex-1 w-full">
                  <CallsTimeSeries data={metrics.timeSeriesData} />
                </div>
              </div>
            </div>
          </CardContent>
          </Card>
        </div>

        <div data-aos="fade-left" data-aos-duration="1000">
          <Card className="border-slate-200 dark:border-slate-800 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <div className="flex items-center">
                <div className="h-8 w-8 rounded-md bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mr-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-600 dark:text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
                  </svg>
                </div>
                <CardTitle className="text-lg text-slate-800 dark:text-slate-100">Call Types</CardTitle>
              </div>
              <Button variant="outline" size="sm" asChild className="border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800">
                <Link to="/distribution" className="flex items-center">
                  <span>View Distribution</span>
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Link>
              </Button>
            </CardHeader>
            <CardContent className="h-[300px] pt-4">
              <DistributionChart
                title=""
                description=""
                data={metrics.typeData}
                simplified={true}
              />
            </CardContent>
          </Card>
        </div>
      </div>

      <div data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
        <Card className="mb-6 border-slate-200 dark:border-slate-800 shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden">
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-slate-500 to-slate-600"></div>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <div className="flex items-center">
              <div className="h-8 w-8 rounded-md bg-slate-100 dark:bg-slate-800 flex items-center justify-center mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-slate-600 dark:text-slate-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <CardTitle className="text-lg text-slate-800 dark:text-slate-100">Recent Service Calls</CardTitle>
            </div>
            <Button variant="outline" size="sm" asChild className="border-slate-200 dark:border-slate-700 text-slate-600 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800">
              <Link to="/logs" className="flex items-center">
                <span>View All Logs</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <CallTable calls={serviceCalls.slice(0, 5)} />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Index;
