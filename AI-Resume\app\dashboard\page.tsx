"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Upload,
  FileText,
  TrendingUp,
  Clock,
  Star,
  BarChart3,
  Settings,
  LogOut,
  User,
  Crown,
  Target,
} from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useApp } from "../providers"

export default function DashboardPage() {
  const { state, dispatch } = useApp()
  const router = useRouter()
  const [recentAnalyses, setRecentAnalyses] = useState([
    {
      id: "1",
      fileName: "John_Doe_Resume.pdf",
      jobTitle: "Senior Frontend Developer",
      matchScore: 85,
      createdAt: "2024-01-15T10:30:00Z",
      status: "completed" as const,
    },
    {
      id: "2",
      fileName: "Resume_Updated.pdf",
      jobTitle: "Product Manager",
      matchScore: 72,
      createdAt: "2024-01-14T15:45:00Z",
      status: "completed" as const,
    },
    {
      id: "3",
      fileName: "My_Resume_v3.pdf",
      jobTitle: "UX Designer",
      matchScore: 0,
      createdAt: "2024-01-14T09:20:00Z",
      status: "processing" as const,
    },
  ])

  // Redirect if not authenticated
  useEffect(() => {
    if (!state.user) {
      router.push("/auth/signin")
    }
  }, [state.user, router])

  if (!state.user) {
    return null // or loading spinner
  }

  const handleLogout = () => {
    dispatch({ type: "SET_USER", payload: null })
    router.push("/")
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800"
      case "processing":
        return "bg-yellow-100 text-yellow-800"
      case "failed":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600"
    if (score >= 60) return "text-yellow-600"
    return "text-red-600"
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">ResumeAI</span>
              </Link>
              <nav className="hidden md:flex items-center space-x-6">
                <Link href="/dashboard" className="text-blue-600 font-medium">
                  Dashboard
                </Link>
                <Link href="/dashboard/analyses" className="text-gray-600 hover:text-gray-900">
                  My Analyses
                </Link>
                <Link href="/dashboard/templates" className="text-gray-600 hover:text-gray-900">
                  Templates
                </Link>
              </nav>
            </div>
            <div className="flex items-center space-x-4">
              {state.user.plan === "free" && (
                <Button size="sm" className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
                  <Crown className="w-4 h-4 mr-2" />
                  Upgrade to Pro
                </Button>
              )}
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-gray-600" />
                </div>
                <div className="hidden md:block">
                  <p className="text-sm font-medium text-gray-900">{state.user.name}</p>
                  <p className="text-xs text-gray-500 capitalize">{state.user.plan} Plan</p>
                </div>
              </div>
              <Button variant="ghost" size="sm" onClick={handleLogout}>
                <LogOut className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Welcome back, {state.user.name}!</h1>
          <p className="text-gray-600">Ready to optimize your resume and land your dream job?</p>
        </div>

        {/* Stats Cards */}
        <div className="grid md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Analyses Used</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {state.user.analysesUsed}/{state.user.analysesLimit === 999 ? "∞" : state.user.analysesLimit}
                  </p>
                </div>
                <FileText className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Match Score</p>
                  <p className="text-2xl font-bold text-green-600">78%</p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Improvements Made</p>
                  <p className="text-2xl font-bold text-purple-600">24</p>
                </div>
                <Star className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Time Saved</p>
                  <p className="text-2xl font-bold text-orange-600">12h</p>
                </div>
                <Clock className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/upload" className="block">
                  <Button className="w-full bg-blue-600 hover:bg-blue-700 justify-start">
                    <Upload className="w-4 h-4 mr-2" />
                    New Analysis
                  </Button>
                </Link>
                <Link href="/dashboard/templates" className="block">
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="w-4 h-4 mr-2" />
                    Browse Templates
                  </Button>
                </Link>
                <Link href="/dashboard/settings" className="block">
                  <Button variant="outline" className="w-full justify-start">
                    <Settings className="w-4 h-4 mr-2" />
                    Account Settings
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Usage Limit */}
            {state.user.plan === "free" && (
              <Card className="mt-6 border-orange-200 bg-orange-50">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <Crown className="w-5 h-5 text-orange-600" />
                    <h3 className="font-semibold text-orange-900">Upgrade to Pro</h3>
                  </div>
                  <p className="text-sm text-orange-800 mb-4">
                    You've used {state.user.analysesUsed} of {state.user.analysesLimit} free analyses.
                  </p>
                  <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                    Upgrade Now
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Recent Analyses */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Recent Analyses</CardTitle>
                <Link href="/dashboard/analyses">
                  <Button variant="outline" size="sm">
                    View All
                  </Button>
                </Link>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentAnalyses.map((analysis) => (
                    <div
                      key={analysis.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="w-5 h-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{analysis.fileName}</h4>
                          <p className="text-sm text-gray-600">{analysis.jobTitle}</p>
                          <p className="text-xs text-gray-500">{formatDate(analysis.createdAt)}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge className={getStatusColor(analysis.status)}>{analysis.status}</Badge>
                        {analysis.status === "completed" && (
                          <div className="text-right">
                            <p className={`text-lg font-bold ${getScoreColor(analysis.matchScore)}`}>
                              {analysis.matchScore}%
                            </p>
                            <p className="text-xs text-gray-500">Match Score</p>
                          </div>
                        )}
                        {analysis.status === "completed" && (
                          <Link href={`/results/${analysis.id}`}>
                            <Button size="sm" variant="outline">
                              View Results
                            </Button>
                          </Link>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Tips Section */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>💡 Pro Tips</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Target className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Tailor for Each Job</h4>
                <p className="text-sm text-gray-600">
                  Customize your resume for each application to increase match scores by up to 40%
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Use Action Words</h4>
                <p className="text-sm text-gray-600">
                  Start bullet points with strong action verbs to make your achievements stand out
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Star className="w-6 h-6 text-purple-600" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Quantify Results</h4>
                <p className="text-sm text-gray-600">
                  Include specific numbers and metrics to demonstrate your impact and achievements
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
