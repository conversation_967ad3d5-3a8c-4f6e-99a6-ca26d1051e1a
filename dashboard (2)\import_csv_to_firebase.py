import pandas as pd
import firebase_admin
from firebase_admin import credentials
from firebase_admin import db
import os
import json
import time
from tqdm import tqdm

# Firebase configuration from insights-dashboard
FIREBASE_CONFIG = {
    "apiKey": "AIzaSyAE3zritBmc8cGl2UfECHjqHbMGL9JBtos",
    "authDomain": "atandra-dashboard.firebaseapp.com",
    "projectId": "atandra-dashboard",
    "databaseURL": "https://atandra-dashboard-default-rtdb.firebaseio.com", #// Added database URL
    "storageBucket": "atandra-dashboard.appspot.com", #// Corrected storage bucket
    "messagingSenderId": "************",
    "appId": "1:************:web:e9743594c42e408af0510d"
}

# Initialize Firebase
def initialize_firebase():
    # For Firebase Admin SDK, we need to create a service account
    # Since we don't have the service account key, we'll use the database URL directly
    try:
        # Initialize the app without credentials (will use Google Application Default Credentials)
        # This works when running on Google Cloud or with local credentials
        firebase_admin.initialize_app(options={
            'databaseURL': FIREBASE_CONFIG["databaseURL"]
        })
        print("Firebase initialized successfully!")
    except Exception as e:
        print(f"Error initializing Firebase: {e}")
        print("\nAlternative method: You may need to create a service account key.")
        print("1. Go to Firebase Console > Project Settings > Service Accounts")
        print("2. Click 'Generate New Private Key'")
        print("3. Save the JSON file")

        credentials_path = input("\nEnter path to Firebase service account JSON file: ")

        try:
            cred = credentials.Certificate(credentials_path)
            firebase_admin.initialize_app(cred, {
                'databaseURL': FIREBASE_CONFIG["databaseURL"]
            })
            print("Firebase initialized successfully with service account!")
        except Exception as e2:
            print(f"Error initializing Firebase with service account: {e2}")
            exit(1)

# Process and upload CSV data to Firebase
def import_csv_to_firebase(csv_path, node_name, chunk_size=1000):
    print(f"Reading CSV file: {csv_path}")

    # Check if file exists
    if not os.path.exists(csv_path):
        print(f"Error: File {csv_path} not found")
        return

    # Get file size for progress reporting
    file_size = os.path.getsize(csv_path)
    print(f"File size: {file_size / (1024 * 1024):.2f} MB")

    try:
        # Read the CSV file in chunks to handle large files
        chunk_iterator = pd.read_csv(
            csv_path,
            chunksize=chunk_size,
            encoding='latin1',  # Try different encodings if needed
            low_memory=False,
            on_bad_lines='skip'  # Skip problematic lines
        )

        total_rows = 0
        processed_rows = 0

        # Count total rows for progress reporting
        print("Counting total rows (this may take a while for large files)...")
        for chunk in pd.read_csv(csv_path, chunksize=chunk_size, encoding='latin1', low_memory=False, on_bad_lines='skip'):
            total_rows += len(chunk)

        print(f"Total rows to process: {total_rows}")

        # Process each chunk
        progress_bar = tqdm(total=total_rows, desc="Importing data")

        for i, chunk in enumerate(chunk_iterator):
            # Clean column names (remove spaces, special characters)
            chunk.columns = [col.strip().replace(' ', '_').replace('.', '_').replace('-', '_').lower() for col in chunk.columns]

            # Convert chunk to dictionary
            records = chunk.to_dict(orient='records')

            # Process records in smaller batches to avoid Firebase limitations
            batch_size = 100
            for j in range(0, len(records), batch_size):
                batch = records[j:j+batch_size]

                # Create a batch update
                updates = {}
                for idx, record in enumerate(batch):
                    # Clean the data (handle NaN, None, etc.)
                    clean_record = {k: ('' if pd.isna(v) else v) for k, v in record.items()}

                    # Use a unique key for each record
                    record_key = f"record_{processed_rows + idx}"
                    updates[record_key] = clean_record

                # Update Firebase
                try:
                    db.reference(f'/{node_name}').update(updates)
                except Exception as e:
                    print(f"Error uploading batch {i}, records {j}-{j+len(batch)}: {e}")

                # Update progress
                processed_rows += len(batch)
                progress_bar.update(len(batch))

                # Small delay to avoid overwhelming Firebase
                time.sleep(0.1)

        progress_bar.close()
        print(f"Import completed! {processed_rows} records imported to Firebase under '{node_name}' node.")

    except Exception as e:
        print(f"Error processing CSV: {e}")

if __name__ == "__main__":
    # Initialize Firebase
    initialize_firebase()

    # Get CSV file path
    csv_path = "ServiceCallsData.csv"

    # Get Firebase node name
    node_name =  "serviceCalls"

    # Get chunk size
    try:
        chunk_size = int(input("Enter chunk size for processing (default: 1000): ") or "1000")
    except ValueError:
        chunk_size = 1000

    # Import data
    import_csv_to_firebase(csv_path, node_name, chunk_size)
