
import { useState, useEffect, useMemo } from "react";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { CallTable } from "@/components/dashboard/CallTable";
import { FilterOptions, ServiceCall, subscribeToServiceCalls } from "@/services/serviceCallService";
import { FileText, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";

const Logs = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [searchQuery, setSearchQuery] = useState("");

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Filter service calls by search query
  const filteredCalls = useMemo(() => {
    if (!searchQuery.trim()) return serviceCalls;

    const lowerQuery = searchQuery.toLowerCase();
    return serviceCalls.filter(call =>
      (call.company && call.company.toLowerCase().includes(lowerQuery)) ||
      (call.branch && call.branch.toLowerCase().includes(lowerQuery)) ||
      (call.eng_remarks && call.eng_remarks.toLowerCase().includes(lowerQuery)) ||
      (call.eng_name && call.eng_name.toLowerCase().includes(lowerQuery)) ||
      (call.sc_id && call.sc_id.toString().includes(lowerQuery))
    );
  }, [serviceCalls, searchQuery]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Set up real-time data subscription
  useEffect(() => {
    let unsubscribe: () => void;

    const setupRealtime = () => {
      unsubscribe = subscribeToServiceCalls((calls) => {
        let filteredCalls = calls;

        // Apply current filters to the real-time data
        if (filters.branch) {
          filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
        }
        if (filters.status) {
          filteredCalls = filteredCalls.filter(call =>
            call.status === filters.status || call.callStatus === filters.status
          );
        }
        if (filters.callType) {
          if (filters.callType === 'ASP') {
            filteredCalls = filteredCalls.filter(call => call.is_aspcall);
          } else {
            filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
          }
        }
        if (filters.startDate && filters.endDate) {
          filteredCalls = filteredCalls.filter(call => {
            const callDate = new Date(call.sc_created).toISOString().split('T')[0];
            return callDate >= filters.startDate! && callDate <= filters.endDate!;
          });
        }

        setServiceCalls(filteredCalls);
        setIsLoading(false);
      });
    };

    setupRealtime();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters]);

  return (
    <div>
      <div data-aos="fade-right" data-aos-duration="800">
        <h1 className="mb-6 text-2xl font-bold">Call Logs</h1>
      </div>

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 flex flex-col sm:flex-row gap-4 items-center" data-aos="fade-up" data-aos-duration="800">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by customer, branch, remarks or engineer..."
            className="pl-8"
            value={searchQuery}
            onChange={handleSearchChange}
          />
        </div>
        <Button variant="outline" onClick={() => setSearchQuery("")}>
          Clear
        </Button>
      </div>

      <div data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Service Call Logs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-2">
              {filteredCalls.length} service calls found {searchQuery ? 'matching your search' : 'for selected filters'}
            </p>

            {filteredCalls.length === 0 ? (
              <div className="text-center py-10 text-muted-foreground">
                No service calls found with the current filters and search criteria.
              </div>
            ) : (
              <div>
                <CallTable calls={filteredCalls} />
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Logs;
