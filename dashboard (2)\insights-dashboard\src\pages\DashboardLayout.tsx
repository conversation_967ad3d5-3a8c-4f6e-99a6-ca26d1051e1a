
import { useState, useEffect } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { DashboardSidebar } from "@/components/dashboard/Sidebar";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { LogOut, BellRing, Search, User, BrainCircuit, Settings } from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ThemeToggle } from "@/components/ThemeToggle";

export default function DashboardLayout() {
  const { toast } = useToast();
  const { signOut } = useAuth();
  const navigate = useNavigate();
  const [isConnected, setIsConnected] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<any[]>([]);

  useEffect(() => {
    // Simulate checking Firebase connection
    const timeout = setTimeout(() => {
      setIsConnected(true);
      toast({
        title: "Connected to Firebase",
        description: "Real-time data synchronization is active.",
      });
    }, 1000);

    return () => clearTimeout(timeout);
  }, [toast]);

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to sign out. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);

    // If query is empty, clear results
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    // Navigate to search results page when query is at least 3 characters
    if (query.length > 2) {
      // Small delay to prevent navigation while still typing
      const debounceTimeout = setTimeout(() => {
        navigate(`/search?q=${encodeURIComponent(query)}`);
      }, 300);

      return () => clearTimeout(debounceTimeout);
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-slate-50 dark:bg-slate-950">
        <DashboardSidebar />
        <div className="flex-1 overflow-auto flex flex-col">
          <header className="sticky top-0 z-10 flex h-16 items-center gap-4 border-b bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm px-6 shadow-sm">
            <SidebarTrigger className="text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100" />

            <div className="flex-1 flex items-center">
              <div className="flex items-center mr-4">
                <div className="hidden md:flex items-center mr-2">
                  <div className="h-8 w-8 rounded-md bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mr-2">
                    <BrainCircuit className="h-5 w-5 text-white" />
                  </div>
                  <h1 className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Atandra Insights</h1>
                </div>
              </div>

              <div className="hidden md:flex relative max-w-md">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
                <Input
                  type="search"
                  placeholder="Search dashboard..."
                  value={searchQuery}
                  onChange={handleSearch}
                  className="pl-9 bg-slate-100 border-slate-200 dark:bg-slate-800 dark:border-slate-700 w-full max-w-xs focus-visible:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center bg-slate-100 dark:bg-slate-800 px-3 py-1.5 rounded-full">
                <span className={`mr-2 h-2 w-2 rounded-full ${isConnected ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`}></span>
                <span className="text-sm text-slate-600 dark:text-slate-300">
                  {isConnected ? 'Connected' : 'Connecting...'}
                </span>
              </div>

              <ThemeToggle />

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="relative">
                    <BellRing className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                    <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-[10px] font-medium flex items-center justify-center text-white">3</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-80">
                  <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <div className="max-h-80 overflow-auto">
                    <div className="flex items-start gap-4 p-3 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer">
                      <div className="h-2 w-2 mt-2 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="text-sm font-medium">New service call received</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">A new service call has been assigned to your team</p>
                        <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">5 minutes ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4 p-3 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer">
                      <div className="h-2 w-2 mt-2 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="text-sm font-medium">System update completed</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">The system has been updated to version 2.1.0</p>
                        <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">1 hour ago</p>
                      </div>
                    </div>
                    <div className="flex items-start gap-4 p-3 hover:bg-slate-100 dark:hover:bg-slate-800 cursor-pointer">
                      <div className="h-2 w-2 mt-2 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="text-sm font-medium">Weekly report available</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Your weekly service call report is now available</p>
                        <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">Yesterday</p>
                      </div>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                  <div className="p-2">
                    <Button variant="outline" size="sm" className="w-full text-xs">View all notifications</Button>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-full h-8 w-8 bg-slate-100 dark:bg-slate-800">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-indigo-600 text-white text-sm">AD</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>My Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleSignOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          <main className="flex-1 p-6 overflow-auto">
            <Outlet />
          </main>

          <footer className="border-t border-slate-200 dark:border-slate-800 py-4 px-6 text-center text-sm text-slate-500 dark:text-slate-400">
            <p>© 2025 Atandra Energy Private Limited. All rights reserved.</p>
          </footer>
        </div>
        <Toaster />
      </div>
    </SidebarProvider>
  );
}
