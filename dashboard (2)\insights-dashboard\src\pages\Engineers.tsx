
import { useState, useEffect, useMemo } from "react";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { FilterOptions, ServiceCall, subscribeToServiceCalls } from "@/services/serviceCallService";
import { Users, CheckCircle, XCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

interface EngineerStats {
  id: string;
  name: string;
  totalCalls: number;
  closedCalls: number;
  openCalls: number;
  avgResolutionTime?: number; // In hours
}

const Engineers = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Calculate engineer statistics
  const engineerStats = useMemo(() => {
    // Group calls by engineer ID
    const callsByEngineer = serviceCalls.reduce((acc: {[key: string]: ServiceCall[]}, call) => {
      const engineerId = call.uid || 'unknown';
      acc[engineerId] = acc[engineerId] || [];
      acc[engineerId].push(call);
      return acc;
    }, {});

    // Calculate statistics for each engineer
    return Object.entries(callsByEngineer).map(([engineerId, calls]) => {
      // Get a sample call to extract engineer name (if available)
      const sampleCall = calls[0];
      const engineerName = sampleCall.eng_name || sampleCall.uid || 'Unknown Engineer';

      // Count open and closed calls
      const closedCalls = calls.filter(call =>
        call.status === 'closed' || call.callStatus === 'closed'
      ).length;

      const openCalls = calls.filter(call =>
        call.status === 'open' || call.callStatus === 'open'
      ).length;

      return {
        id: engineerId,
        name: engineerName,
        totalCalls: calls.length,
        closedCalls,
        openCalls,
        // We're not calculating avg resolution time for simplicity
      };
    }).sort((a, b) => b.totalCalls - a.totalCalls);
  }, [serviceCalls]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Set up real-time data subscription
  useEffect(() => {
    let unsubscribe: () => void;

    const setupRealtime = () => {
      unsubscribe = subscribeToServiceCalls((calls) => {
        let filteredCalls = calls;

        // Apply current filters to the real-time data
        if (filters.branch) {
          filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
        }
        if (filters.status) {
          filteredCalls = filteredCalls.filter(call =>
            call.status === filters.status || call.callStatus === filters.status
          );
        }
        if (filters.callType) {
          if (filters.callType === 'ASP') {
            filteredCalls = filteredCalls.filter(call => call.is_aspcall);
          } else {
            filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
          }
        }
        if (filters.startDate && filters.endDate) {
          filteredCalls = filteredCalls.filter(call => {
            const callDate = new Date(call.sc_created).toISOString().split('T')[0];
            return callDate >= filters.startDate! && callDate <= filters.endDate!;
          });
        }

        setServiceCalls(filteredCalls);
        setIsLoading(false);
      });
    };

    setupRealtime();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters]);

  return (
    <div>
      <div data-aos="fade-right" data-aos-duration="800">
        <h1 className="mb-6 text-2xl font-bold">Engineer Reports</h1>
      </div>

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 grid gap-4 sm:grid-cols-3">
        <div data-aos="fade-right" data-aos-delay="100">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                Total Engineers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{engineerStats.length}</div>
            </CardContent>
          </Card>
        </div>

        <div data-aos="fade-up" data-aos-delay="200">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                Closed Calls
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {engineerStats.reduce((sum, engineer) => sum + engineer.closedCalls, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        <div data-aos="fade-left" data-aos-delay="100">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                Open Calls
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {engineerStats.reduce((sum, engineer) => sum + engineer.openCalls, 0)}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="space-y-6" data-aos="fade-up" data-aos-duration="1000" data-aos-delay="200">
        <h2 className="text-xl font-semibold">Engineer Performance</h2>

        <div className="rounded-lg border bg-card">
          <div className="p-4">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="pb-2 text-left">Engineer</th>
                  <th className="pb-2 text-center">Total Calls</th>
                  <th className="pb-2 text-center">Closed</th>
                  <th className="pb-2 text-center">Open</th>
                  <th className="pb-2 text-center">Completion Rate</th>
                </tr>
              </thead>
              <tbody>
                {engineerStats.map((engineer, i) => (
                  <tr key={i} className={i % 2 === 0 ? "bg-muted/50" : ""}>
                    <td className="py-2">{engineer.name}</td>
                    <td className="py-2 text-center">{engineer.totalCalls}</td>
                    <td className="py-2 text-center">{engineer.closedCalls}</td>
                    <td className="py-2 text-center">{engineer.openCalls}</td>
                    <td className="py-2 px-4">
                      <div className="flex items-center gap-2">
                        <Progress
                          value={Math.round(engineer.closedCalls / engineer.totalCalls * 100)}
                          className="h-2"
                        />
                        <span className="text-sm">
                          {Math.round(engineer.closedCalls / engineer.totalCalls * 100)}%
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Engineers;
