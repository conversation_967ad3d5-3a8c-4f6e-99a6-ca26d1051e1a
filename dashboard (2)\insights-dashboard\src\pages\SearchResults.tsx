import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Filter, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: string;
  date: string;
}

export default function SearchResults() {
  const location = useLocation();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Parse query parameter
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const query = queryParams.get('q') || '';
    setSearchQuery(query);

    if (query) {
      performSearch(query);
    }
  }, [location.search]);

  const performSearch = (query: string) => {
    setIsLoading(true);

    // In a real implementation, this would call your API
    // For now, we'll just simulate the API call with a timeout
    setTimeout(() => {
      // This would be replaced with actual API call results
      // For now, we'll just set empty results
      setResults([]);
      setIsLoading(false);
    }, 500);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const goBack = () => {
    navigate(-1);
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="ghost" size="icon" onClick={goBack}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <h1 className="text-2xl font-bold">Search Results</h1>
        </div>
        <div className="text-sm text-slate-500">
          {results.length} results found for "{searchQuery}"
        </div>
      </div>

      <Card className="bg-white dark:bg-slate-900">
        <CardHeader className="pb-3">
          <CardTitle>Search</CardTitle>
          <CardDescription>Find service calls, reports, and more</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearchSubmit} className="flex space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-slate-400" />
              <Input
                type="search"
                placeholder="Search..."
                className="pl-9"
                value={searchQuery}
                onChange={handleSearchChange}
              />
            </div>
            <Button type="submit">Search</Button>
            <Button variant="outline" type="button">
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </Button>
          </form>
        </CardContent>
      </Card>

      {isLoading ? (
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="space-y-4">
          {results.map((result) => (
            <Card key={result.id} className="hover:bg-slate-50 dark:hover:bg-slate-800/50 cursor-pointer transition-colors">
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-medium">{result.title}</h3>
                    <p className="text-slate-500 dark:text-slate-400 mt-1">{result.description}</p>
                  </div>
                  <div className="flex flex-col items-end">
                    <span className="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                      {result.type}
                    </span>
                    <span className="text-xs text-slate-500 mt-2">{result.date}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {results.length === 0 && !isLoading && searchQuery && (
            <div className="text-center py-12">
              <p className="text-slate-500 dark:text-slate-400">No results found for "{searchQuery}"</p>
              <p className="text-slate-400 dark:text-slate-500 mt-2">The search functionality is connected but no data is available yet.</p>
              <p className="text-slate-400 dark:text-slate-500 mt-1">This is a placeholder for future implementation.</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
