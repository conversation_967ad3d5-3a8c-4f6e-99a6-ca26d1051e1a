import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface CalendarHeatmapProps {
  data: {
    date: string;
    count: number;
  }[];
  title?: string;
  description?: string;
}

export function CalendarHeatmap({ data, title, description }: CalendarHeatmapProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear previous chart
    d3.select(svgRef.current).selectAll('*').remove();

    // Set dimensions
    const width = svgRef.current.clientWidth;
    const height = 150;
    const cellSize = 15;
    const cellMargin = 2;

    // Create SVG
    const svg = d3
      .select(svgRef.current)
      .attr('width', width)
      .attr('height', height);

    // Parse dates
    const parsedData = data.map(d => ({
      date: new Date(d.date),
      count: d.count
    }));

    // Get min and max dates
    const minDate = d3.min(parsedData, d => d.date) || new Date();
    const maxDate = d3.max(parsedData, d => d.date) || new Date();

    // Get min and max values
    const maxValue = d3.max(parsedData, d => d.count) || 0;

    // Create color scale
    const colorScale = d3.scaleSequential()
      .domain([0, maxValue])
      .interpolator(d3.interpolateBlues);

    // Group data by month
    const monthData = d3.groups(parsedData, d => d3.timeMonth(d.date));

    // Create month containers
    const months = svg.selectAll('.month')
      .data(monthData)
      .enter()
      .append('g')
      .attr('class', 'month')
      .attr('transform', (d, i) => `translate(${i * (cellSize * 31 + 20)}, 20)`);

    // Add month labels
    months.append('text')
      .attr('class', 'month-label')
      .attr('y', -5)
      .text(d => d3.timeFormat('%B %Y')(d[0]))
      .style('font-size', '12px')
      .style('font-weight', 'bold');

    // Create day cells
    months.each(function(monthGroup) {
      const month = monthGroup[0];
      const monthData = monthGroup[1];
      
      const daysInMonth = d3.timeDay.range(
        d3.timeMonth(month),
        d3.timeMonth.offset(month, 1)
      );
      
      const days = d3.select(this).selectAll('.day')
        .data(daysInMonth)
        .enter()
        .append('rect')
        .attr('class', 'day')
        .attr('width', cellSize - cellMargin)
        .attr('height', cellSize - cellMargin)
        .attr('x', d => d3.timeDay.count(d3.timeMonth(d), d) * cellSize)
        .attr('y', d => d.getDay() * cellSize)
        .attr('fill', d => {
          const dayData = monthData.find(md => d3.timeDay(md.date).getTime() === d3.timeDay(d).getTime());
          return dayData ? colorScale(dayData.count) : '#eee';
        })
        .attr('stroke', '#fff')
        .attr('stroke-width', 1)
        .append('title')
        .text(d => {
          const dayData = monthData.find(md => d3.timeDay(md.date).getTime() === d3.timeDay(d).getTime());
          return `${d3.timeFormat('%Y-%m-%d')(d)}: ${dayData ? dayData.count : 0} calls`;
        });
    });

    // Add day of week labels
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    svg.selectAll('.weekday')
      .data(weekdays)
      .enter()
      .append('text')
      .attr('class', 'weekday')
      .attr('x', 0)
      .attr('y', (d, i) => i * cellSize + cellSize / 2 + 25)
      .style('text-anchor', 'end')
      .style('font-size', '10px')
      .text(d => d);

  }, [data, title, description]);

  return (
    <div className="w-full h-full flex flex-col">
      {title && <h3 className="text-sm font-medium mb-2">{title}</h3>}
      {description && <p className="text-xs text-muted-foreground mb-4">{description}</p>}
      <div className="flex-1 overflow-x-auto">
        <svg ref={svgRef} width="100%" height="150" />
      </div>
    </div>
  );
}
