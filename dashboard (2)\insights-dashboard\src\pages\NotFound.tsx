
import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const NotFound = () => {
  const location = useLocation();
  const { currentUser } = useAuth();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background">
      <div data-aos="zoom-in" data-aos-duration="1000" className="mb-8 flex h-20 w-20 items-center justify-center rounded-full bg-muted">
        <AlertCircle className="h-10 w-10 text-muted-foreground" />
      </div>
      <h1 data-aos="fade-up" data-aos-duration="800" data-aos-delay="200" className="mb-2 text-4xl font-bold">404</h1>
      <p data-aos="fade-up" data-aos-duration="800" data-aos-delay="400" className="mb-8 text-xl text-muted-foreground">
        Oops! The page you're looking for doesn't exist.
      </p>
      <div data-aos="fade-up" data-aos-duration="800" data-aos-delay="600">
        <Button asChild>
          <Link to={currentUser ? "/" : "/login"}>
            {currentUser ? "Back to Dashboard" : "Back to Login"}
          </Link>
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
