
import { useState, useEffect, useMemo } from "react";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { FilterOptions, ServiceCall, subscribeToServiceCalls } from "@/services/serviceCallService";
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

const Calendar = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [currentDate, setCurrentDate] = useState(new Date());

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Generate calendar data
  const calendarData = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    const startingDayOfWeek = firstDay.getDay(); // 0 is Sunday, 1 is Monday, etc.

    // Get the number of days in the month
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();

    // Create an array for the days in the month
    const days = [];

    // Add empty cells for the days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push({ day: null, calls: [] });
    }

    // Add the days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateString = date.toISOString().split('T')[0];

      // Find calls for this date
      const callsOnDay = serviceCalls.filter(call => {
        const callDate = new Date(call.sc_created).toISOString().split('T')[0];
        return callDate === dateString;
      });

      days.push({
        day,
        date: dateString,
        calls: callsOnDay,
      });
    }

    return days;
  }, [currentDate, serviceCalls]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Calendar navigation
  const prevMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1));
  };

  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1));
  };

  const currentMonth = currentDate.toLocaleString('default', { month: 'long' });
  const currentYear = currentDate.getFullYear();

  // Set up real-time data subscription
  useEffect(() => {
    let unsubscribe: () => void;

    const setupRealtime = () => {
      unsubscribe = subscribeToServiceCalls((calls) => {
        let filteredCalls = calls;

        // Apply current filters to the real-time data
        if (filters.branch) {
          filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
        }
        if (filters.status) {
          filteredCalls = filteredCalls.filter(call =>
            call.status === filters.status || call.callStatus === filters.status
          );
        }
        if (filters.callType) {
          if (filters.callType === 'ASP') {
            filteredCalls = filteredCalls.filter(call => call.is_aspcall);
          } else {
            filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
          }
        }
        if (filters.startDate && filters.endDate) {
          filteredCalls = filteredCalls.filter(call => {
            const callDate = new Date(call.sc_created).toISOString().split('T')[0];
            return callDate >= filters.startDate! && callDate <= filters.endDate!;
          });
        }

        setServiceCalls(filteredCalls);
        setIsLoading(false);
      });
    };

    setupRealtime();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters]);

  return (
    <div>
      <div data-aos="fade-right" data-aos-duration="800">
        <h1 className="mb-6 text-2xl font-bold">Call Calendar</h1>
      </div>

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 flex items-center justify-between" data-aos="fade-up" data-aos-duration="800">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <CalendarIcon className="h-5 w-5" />
          {currentMonth} {currentYear}
        </h2>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={prevMonth}>
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <Button variant="outline" size="sm" onClick={nextMonth}>
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="rounded-lg border bg-card" data-aos="zoom-in" data-aos-duration="1000">
        <div className="grid grid-cols-7 border-b text-center font-medium">
          <div className="p-2 border-r">Sunday</div>
          <div className="p-2 border-r">Monday</div>
          <div className="p-2 border-r">Tuesday</div>
          <div className="p-2 border-r">Wednesday</div>
          <div className="p-2 border-r">Thursday</div>
          <div className="p-2 border-r">Friday</div>
          <div className="p-2">Saturday</div>
        </div>

        <div className="grid grid-cols-7 auto-rows-fr">
          {calendarData.map((dayInfo, index) => (
            <div
              key={index}
              className={cn(
                "min-h-28 border-r border-b p-1 last:border-r-0",
                index % 7 === 6 ? "border-r-0" : "",
                !dayInfo.day ? "bg-muted/20" : ""
              )}
            >
              {dayInfo.day && (
                <>
                  <div className="font-medium mb-1">{dayInfo.day}</div>
                  <div>
                    {dayInfo.calls.length > 0 && (
                      <div className="text-xs">
                        <span className="bg-primary text-primary-foreground text-xs px-1 py-0.5 rounded">
                          {dayInfo.calls.length} calls
                        </span>
                      </div>
                    )}
                    <div className="mt-1 space-y-1 overflow-y-auto max-h-20">
                      {dayInfo.calls.slice(0, 3).map((call, idx) => (
                        <div key={idx} className="text-xs p-1 bg-muted/50 rounded truncate">
                          {call.branch}: {call.company || 'Unknown'}
                        </div>
                      ))}
                      {dayInfo.calls.length > 3 && (
                        <div className="text-xs text-muted-foreground">
                          +{dayInfo.calls.length - 3} more
                        </div>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Calendar;
