
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

export function generateChartColors(count: number) {
  const colors = [
    '#3b82f6', // blue
    '#10b981', // green
    '#f97316', // orange
    '#ef4444', // red
    '#8b5cf6', // purple
    '#14b8a6', // teal
    '#6366f1', // indigo
    '#ec4899', // pink
    '#f59e0b', // amber
    '#84cc16', // lime
  ];
  
  // If we need more colors than available, cycle through them
  return Array.from({ length: count }, (_, i) => colors[i % colors.length]);
}

export function getStatusColor(status: string): string {
  status = status.toLowerCase();
  if (status === 'open') return 'bg-dashboard-green';
  if (status === 'closed') return 'bg-dashboard-red';
  if (status === 'in progress') return 'bg-dashboard-orange';
  if (status === 'pending') return 'bg-dashboard-purple';
  return 'bg-dashboard-blue';
}

export function truncateText(text: string, length: number): string {
  if (!text) return '';
  if (text.length <= length) return text;
  return text.substring(0, length) + '...';
}
