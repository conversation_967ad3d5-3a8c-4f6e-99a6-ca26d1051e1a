import React, { useEffect, useRef } from 'react';
import * as d3 from 'd3';

interface HeatMapProps {
  data: {
    date: string;
    value: number;
  }[];
  title?: string;
  description?: string;
}

export function HeatMapChart({ data, title, description }: HeatMapProps) {
  const svgRef = useRef<SVGSVGElement>(null);

  useEffect(() => {
    if (!svgRef.current || !data.length) return;

    // Clear previous chart
    d3.select(svgRef.current).selectAll('*').remove();

    // Set dimensions
    const margin = { top: 20, right: 30, bottom: 30, left: 40 };
    const width = svgRef.current.clientWidth - margin.left - margin.right;
    const height = 200 - margin.top - margin.bottom;

    // Create SVG
    const svg = d3
      .select(svgRef.current)
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Parse dates and prepare data
    const parsedData = data.map(d => ({
      date: new Date(d.date),
      value: d.value
    }));

    // Get min and max dates
    const minDate = d3.min(parsedData, d => d.date) || new Date();
    const maxDate = d3.max(parsedData, d => d.date) || new Date();

    // Get min and max values
    const maxValue = d3.max(parsedData, d => d.value) || 0;

    // Create scales
    const xScale = d3.scaleTime()
      .domain([minDate, maxDate])
      .range([0, width]);

    // Create color scale
    const colorScale = d3.scaleSequential()
      .domain([0, maxValue])
      .interpolator(d3.interpolateBlues);

    // Create cell width
    const cellWidth = width / parsedData.length;
    const cellHeight = height;

    // Create cells
    svg.selectAll('rect')
      .data(parsedData)
      .enter()
      .append('rect')
      .attr('x', d => xScale(d.date) - cellWidth / 2)
      .attr('width', cellWidth)
      .attr('y', 0)
      .attr('height', cellHeight)
      .attr('fill', d => colorScale(d.value))
      .attr('stroke', '#fff')
      .attr('stroke-width', 1)
      .append('title')
      .text(d => `${d.date.toLocaleDateString()}: ${d.value} calls`);

    // Add x-axis
    const xAxis = d3.axisBottom(xScale)
      .ticks(5)
      .tickFormat(d => d3.timeFormat('%b %d')(d as Date));

    svg.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(xAxis);

    // Add title
    if (title) {
      svg.append('text')
        .attr('x', width / 2)
        .attr('y', -5)
        .attr('text-anchor', 'middle')
        .style('font-size', '14px')
        .text(title);
    }

  }, [data, title, description]);

  return (
    <div className="w-full h-full flex flex-col">
      {title && <h3 className="text-sm font-medium mb-2">{title}</h3>}
      {description && <p className="text-xs text-muted-foreground mb-4">{description}</p>}
      <div className="flex-1 min-h-[200px]">
        <svg ref={svgRef} width="100%" height="100%" />
      </div>
    </div>
  );
}
