import pandas as pd
import firebase_admin
from firebase_admin import credentials
from firebase_admin import db
import os
import json
import time
from tqdm import tqdm

# Firebase configuration from insights-dashboard
FIREBASE_CONFIG = {
    "apiKey": "AIzaSyAxO5AVxrHfPYlCr0BsDtxslaX-l11DsUc",
    "authDomain": "gen-lang-client-**********.firebaseapp.com",
    "databaseURL": "https://gen-lang-client-**********-default-rtdb.firebaseio.com",
    "projectId": "gen-lang-client-**********",
    "storageBucket": "gen-lang-client-**********.firebasestorage.app",
    "messagingSenderId": "************",
    "appId": "1:************:web:e67b84ff7a197313a8265d"
}

# Initialize Firebase with service account
def initialize_firebase():
    # Ask for service account key file path
    service_account_path = "gen-lang-client-**********-firebase-adminsdk-fbsvc-4a9bc073d4.json"

    if not os.path.exists(service_account_path):
        print(f"Error: File {service_account_path} not found")
        exit(1)

    try:
        # Initialize Firebase with service account
        cred = credentials.Certificate(service_account_path)
        firebase_admin.initialize_app(cred, {
            'databaseURL': FIREBASE_CONFIG["databaseURL"]
        })
        print("Firebase initialized successfully!")
    except Exception as e:
        print(f"Error initializing Firebase: {e}")
        exit(1)

# Save checkpoint to file
def save_checkpoint(processed_rows):
    with open('import_checkpoint.txt', 'w') as f:
        f.write(str(processed_rows))
    print(f"Checkpoint saved: {processed_rows} rows processed")

# Load checkpoint from file
def load_checkpoint():
    try:
        with open('import_checkpoint.txt', 'r') as f:
            return int(f.read().strip())
    except (FileNotFoundError, ValueError):
        return 0

# Process and upload CSV data to Firebase
def import_csv_to_firebase(csv_path, node_name, chunk_size=1000, resume_from=0):
    print(f"Reading CSV file: {csv_path}")

    # Check if file exists
    if not os.path.exists(csv_path):
        print(f"Error: File {csv_path} not found")
        return

    # Get file size for progress reporting
    file_size = os.path.getsize(csv_path)
    print(f"File size: {file_size / (1024 * 1024):.2f} MB")

    try:
        # Count total rows for progress reporting
        print("Counting total rows (this may take a while for large files)...")
        total_rows = 0
        for chunk in pd.read_csv(csv_path, chunksize=chunk_size, encoding='latin1', low_memory=False, on_bad_lines='skip'):
            total_rows += len(chunk)

        print(f"Total rows to process: {total_rows}")

        if resume_from > 0:
            print(f"Resuming from row {resume_from}")
            if resume_from >= total_rows:
                print("Resume point is beyond the total number of rows. Nothing to process.")
                return

        # Read the CSV file in chunks to handle large files
        chunk_iterator = pd.read_csv(
            csv_path,
            chunksize=chunk_size,
            encoding='latin1',  # Try different encodings if needed
            low_memory=False,
            on_bad_lines='skip',  # Skip problematic lines
            skiprows=range(1, resume_from + 1) if resume_from > 0 else None
        )

        processed_rows = resume_from

        # Process each chunk
        progress_bar = tqdm(total=total_rows, desc="Importing data", initial=resume_from)

        for i, chunk in enumerate(chunk_iterator):
            # Clean column names (remove spaces, special characters)
            chunk.columns = [col.strip().replace(' ', '_').replace('.', '_').replace('-', '_').lower() for col in chunk.columns]

            # Convert chunk to dictionary
            records = chunk.to_dict(orient='records')

            # Process records in smaller batches to avoid Firebase limitations
            batch_size = 100
            for j in range(0, len(records), batch_size):
                batch = records[j:j+batch_size]

                # Create a batch update
                updates = {}
                for idx, record in enumerate(batch):
                    # Clean the data (handle NaN, None, etc.)
                    clean_record = {k: ('' if pd.isna(v) else v) for k, v in record.items()}

                    # Use a unique key for each record
                    record_key = f"record_{processed_rows + idx}"
                    updates[record_key] = clean_record

                # Update Firebase with retry logic
                max_retries = 5  # Increased from 3 to 5
                success = False
                for retry in range(max_retries):
                    try:
                        db.reference(f'/{node_name}').update(updates)
                        success = True
                        break  # Success, exit retry loop
                    except Exception as e:
                        if retry < max_retries - 1:
                            print(f"Error uploading batch {i}, records {j}-{j+len(batch)}: {e}")
                            print(f"Retrying ({retry + 1}/{max_retries})...")
                            # Longer exponential backoff
                            backoff_time = 3 * (2 ** retry)  # 3, 6, 12, 24, 48 seconds
                            print(f"Waiting {backoff_time} seconds before retry...")
                            time.sleep(backoff_time)
                        else:
                            print(f"Failed to upload batch {i}, records {j}-{j+len(batch)} after {max_retries} attempts: {e}")
                            # Continue with next batch instead of failing the entire import

                # If upload was successful, print a success message and save checkpoint periodically
                if success and (processed_rows % 1000 == 0):
                    print(f"Successfully uploaded {processed_rows} records so far...")
                    save_checkpoint(processed_rows)

                # Update progress
                processed_rows += len(batch)
                progress_bar.update(len(batch))

                # Small delay to avoid overwhelming Firebase
                time.sleep(0.1)

        progress_bar.close()
        print(f"Import completed! {processed_rows} records imported to Firebase under '{node_name}' node.")

        # Save final checkpoint
        save_checkpoint(processed_rows)
        print("Final checkpoint saved.")

        # Write completion file
        with open('import_completed.txt', 'w') as f:
            f.write(f"Import completed on {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Total records imported: {processed_rows}\n")
            f.write(f"CSV file: {csv_path}\n")
            f.write(f"Firebase node: {node_name}\n")
        print("Import completion record saved to 'import_completed.txt'")

    except Exception as e:
        print(f"Error processing CSV: {e}")
        print(f"Processed {processed_rows} records before error occurred.")
        print(f"You can resume from row {processed_rows} by running the script again with resume_from={processed_rows}")

if __name__ == "__main__":
    # Initialize Firebase
    initialize_firebase()

    # Hardcoded parameters
    csv_path = "ServiceCallsData.csv"  # Path to your CSV file
    node_name = "serviceCalls"        # Firebase node name to store data
    chunk_size = 10000                # Process 10,000 rows at a time

    # Check for checkpoint file or use provided resume point
    checkpoint = load_checkpoint()
    resume_from = checkpoint if checkpoint > 0 else 107600  # Use checkpoint or start from 107600

    # Import data
    print(f"Starting import with the following parameters:")
    print(f"CSV file: {csv_path}")
    print(f"Firebase node: {node_name}")
    print(f"Chunk size: {chunk_size}")
    print(f"Resuming from row: {resume_from}")
    if checkpoint > 0:
        print(f"(Resuming from checkpoint file)")
    print("")

    try:
        import_csv_to_firebase(csv_path, node_name, chunk_size, resume_from)
    except KeyboardInterrupt:
        print("\nImport process interrupted by user.")
        print(f"You can resume from the last checkpoint by running the script again.")
    except Exception as e:
        print(f"\nError during import: {e}")
        print(f"You can resume from the last checkpoint by running the script again.")
