"use client"

import type React from "react"

import { useState, useCallback, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, FileText, X, CheckCircle, AlertCircle, ArrowLeft, Briefcase } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useApp } from "../providers"

export default function UploadPage() {
  const { state, dispatch } = useApp()
  const router = useRouter()
  const [dragActive, setDragActive] = useState(false)
  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [jobDescription, setJobDescription] = useState("")
  const [jobTitle, setJobTitle] = useState("")
  const [uploadError, setUploadError] = useState("")
  const [isUploading, setIsUploading] = useState(false)

  // Redirect if not authenticated
  useEffect(() => {
    if (!state.user) {
      router.push("/auth/signin")
    }
  }, [state.user, router])

  // Check usage limits
  useEffect(() => {
    if (state.user && state.user.plan === "free" && state.user.analysesUsed >= state.user.analysesLimit) {
      setUploadError("You've reached your free analysis limit. Please upgrade to continue.")
    }
  }, [state.user])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    setUploadError("")

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]
      validateAndSetFile(file)
    }
  }, [])

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      validateAndSetFile(file)
    }
  }

  const validateAndSetFile = (file: File) => {
    const allowedTypes = [
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    ]
    const maxSize = 5 * 1024 * 1024 // 5MB

    if (!allowedTypes.includes(file.type)) {
      setUploadError("Please upload a PDF or Word document")
      return
    }

    if (file.size > maxSize) {
      setUploadError("File size must be less than 5MB")
      return
    }

    setUploadedFile(file)
    setUploadError("")
  }

  const removeFile = () => {
    setUploadedFile(null)
    setUploadError("")
  }

  const handleAnalyze = async () => {
    if (!uploadedFile || !jobDescription.trim() || !jobTitle.trim()) {
      setUploadError("Please upload a resume, provide a job title, and job description")
      return
    }

    if (state.user && state.user.plan === "free" && state.user.analysesUsed >= state.user.analysesLimit) {
      setUploadError("You've reached your free analysis limit. Please upgrade to continue.")
      return
    }

    setIsUploading(true)

    // Create new analysis
    const newAnalysis = {
      id: Math.random().toString(36).substr(2, 9),
      fileName: uploadedFile.name,
      jobTitle: jobTitle,
      matchScore: 0,
      createdAt: new Date().toISOString(),
      status: "processing" as const,
      improvements: [],
      skillGaps: [],
    }

    dispatch({ type: "SET_CURRENT_ANALYSIS", payload: newAnalysis })
    dispatch({ type: "ADD_ANALYSIS", payload: newAnalysis })

    // Update user's usage count
    if (state.user) {
      const updatedUser = {
        ...state.user,
        analysesUsed: state.user.analysesUsed + 1,
      }
      dispatch({ type: "SET_USER", payload: updatedUser })
    }

    // Simulate upload and processing
    setTimeout(() => {
      router.push(`/processing/${newAnalysis.id}`)
    }, 1000)
  }

  if (!state.user) {
    return null // or loading spinner
  }

  const canAnalyze = state.user.plan !== "free" || state.user.analysesUsed < state.user.analysesLimit

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
            <span className="text-gray-600">Back to Dashboard</span>
          </Link>
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Upload className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">ResumeAI</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                  1
                </div>
                <span className="ml-2 text-blue-600 font-medium">Upload</span>
              </div>
              <div className="w-16 h-1 bg-gray-200 rounded"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-200 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">
                  2
                </div>
                <span className="ml-2 text-gray-500">Analysis</span>
              </div>
              <div className="w-16 h-1 bg-gray-200 rounded"></div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gray-200 text-gray-500 rounded-full flex items-center justify-center text-sm font-semibold">
                  3
                </div>
                <span className="ml-2 text-gray-500">Results</span>
              </div>
            </div>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Upload Your Resume</h1>
            <p className="text-lg text-gray-600">Upload your resume and provide the job details you're targeting</p>
            {!canAnalyze && (
              <div className="mt-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                <p className="text-orange-800">
                  You've used all your free analyses.{" "}
                  <Link href="/pricing" className="text-orange-600 underline">
                    Upgrade to Pro
                  </Link>{" "}
                  for unlimited analyses.
                </p>
              </div>
            )}
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Resume Upload */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Resume Upload
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div
                  className={`border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200 ${
                    dragActive
                      ? "border-blue-400 bg-blue-50"
                      : uploadedFile
                        ? "border-green-400 bg-green-50"
                        : "border-gray-300 hover:border-gray-400"
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  {uploadedFile ? (
                    <div className="space-y-4">
                      <CheckCircle className="w-12 h-12 text-green-600 mx-auto" />
                      <div>
                        <p className="font-medium text-gray-900">{uploadedFile.name}</p>
                        <p className="text-sm text-gray-500">{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={removeFile}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <X className="w-4 h-4 mr-1" />
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <Upload className="w-12 h-12 text-gray-400 mx-auto" />
                      <div>
                        <p className="text-lg font-medium text-gray-900 mb-2">Drop your resume here</p>
                        <p className="text-gray-500 mb-4">or click to browse files</p>
                        <input
                          type="file"
                          accept=".pdf,.doc,.docx"
                          onChange={handleFileInput}
                          className="hidden"
                          id="file-upload"
                          disabled={!canAnalyze}
                        />
                        <label htmlFor="file-upload">
                          <Button variant="outline" className="cursor-pointer" disabled={!canAnalyze}>
                            Browse Files
                          </Button>
                        </label>
                      </div>
                      <p className="text-xs text-gray-400">Supports PDF, DOC, DOCX (max 5MB)</p>
                    </div>
                  )}
                </div>

                {uploadError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center gap-2">
                    <AlertCircle className="w-4 h-4 text-red-600" />
                    <p className="text-sm text-red-700">{uploadError}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Job Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="w-5 h-5" />
                  Job Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    placeholder="e.g., Senior Frontend Developer"
                    value={jobTitle}
                    onChange={(e) => setJobTitle(e.target.value)}
                    disabled={!canAnalyze}
                  />
                </div>
                <div>
                  <Label htmlFor="jobDescription">Job Description</Label>
                  <Textarea
                    id="jobDescription"
                    placeholder="Paste the job description here...

Example:
We are looking for a Senior Marketing Manager with 5+ years of experience in digital marketing, content strategy, and team leadership. The ideal candidate should have expertise in SEO, SEM, social media marketing, and analytics tools like Google Analytics and HubSpot..."
                    value={jobDescription}
                    onChange={(e) => setJobDescription(e.target.value)}
                    className="min-h-[300px] resize-none"
                    disabled={!canAnalyze}
                  />
                  <div className="mt-2 flex justify-between items-center text-sm text-gray-500">
                    <span>{jobDescription.length} characters</span>
                    <span>Minimum 100 characters recommended</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="mt-8 flex justify-center gap-4">
            <Link href="/dashboard">
              <Button variant="outline" size="lg" className="px-8">
                Back to Dashboard
              </Button>
            </Link>
            <Button
              size="lg"
              className="bg-blue-600 hover:bg-blue-700 px-8"
              onClick={handleAnalyze}
              disabled={!uploadedFile || !jobDescription.trim() || !jobTitle.trim() || isUploading || !canAnalyze}
            >
              {isUploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  Starting Analysis...
                </>
              ) : (
                "Analyze Resume"
              )}
            </Button>
          </div>

          {/* Usage Info */}
          <div className="mt-8 text-center">
            <div className="inline-flex items-center gap-4 text-sm text-gray-500 bg-gray-50 px-4 py-2 rounded-lg">
              <span>🔒 Your resume is processed securely and deleted after analysis</span>
              <span>•</span>
              <span>
                Analyses used: {state.user.analysesUsed}/
                {state.user.analysesLimit === 999 ? "∞" : state.user.analysesLimit}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
