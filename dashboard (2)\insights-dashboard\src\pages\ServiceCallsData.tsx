import { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { StatCard } from "@/components/dashboard/StatCard";
import { CallsTimeSeries } from "@/components/dashboard/CallsTimeSeries";
import { DistributionChart } from "@/components/dashboard/DistributionChart";
import { FilterBar } from "@/components/dashboard/FilterBar";
import { CallTable } from "@/components/dashboard/CallTable";
import { HeatMapChart } from "@/components/dashboard/HeatMapChart";
import { RadarChart } from "@/components/dashboard/RadarChart";
import { CalendarHeatmap } from "@/components/dashboard/CalendarHeatmap";
import { FilterOptions, ServiceCall, subscribeToServiceCalls, fetchServiceCalls } from "@/services/serviceCallService";
import { Activity, Clock, CheckCircle, AlertCircle, Users, Building, BarChart3, FileText } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";

const ServiceCallsData = () => {
  const [serviceCalls, setServiceCalls] = useState<ServiceCall[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState<FilterOptions>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("overview");

  // Extract unique branches, statuses, and call types for filters
  const branches = useMemo(() => {
    const branchSet = new Set(serviceCalls.map(call => call.branch));
    return Array.from(branchSet);
  }, [serviceCalls]);

  const statuses = useMemo(() => {
    const statusSet = new Set(serviceCalls.map(call => call.status || call.callStatus || '').filter(Boolean));
    return Array.from(statusSet);
  }, [serviceCalls]);

  const callTypes = useMemo(() => {
    const typeSet = new Set(
      serviceCalls.map(call => call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular'))
    );
    return Array.from(typeSet);
  }, [serviceCalls]);

  // Calculate metrics
  const metrics = useMemo(() => {
    // Total calls
    const totalCalls = serviceCalls.length;

    // Calls by status
    const openCalls = serviceCalls.filter(call =>
      (call.status === 'open' || call.callStatus === 'open')
    ).length;

    const closedCalls = serviceCalls.filter(call =>
      (call.status === 'closed' || call.callStatus === 'closed')
    ).length;

    const inProgressCalls = totalCalls - openCalls - closedCalls;

    // Calls by branch
    const callsByBranch = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const branch = call.branch || 'Unknown';
      acc[branch] = (acc[branch] || 0) + 1;
      return acc;
    }, {});

    const branchData = Object.entries(callsByBranch)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);

    const topBranches = branchData.slice(0, 10); // Top 10 branches

    // Calls by type
    const callsByType = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const type = call.is_aspcall ? 'ASP' : (call.sc_type || 'Regular');
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {});

    const typeData = Object.entries(callsByType).map(([name, value]) => ({ name, value }));

    // Calls by rating
    const callsByRating = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const rating = call.rating || 'Unknown';
      acc[rating] = (acc[rating] || 0) + 1;
      return acc;
    }, {});

    const ratingData = Object.entries(callsByRating)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);

    const topRatings = ratingData.slice(0, 8); // Top 8 ratings

    // Get date range for all calls
    const dates = serviceCalls.map(call => new Date(call.sc_created)).filter(d => !isNaN(d.getTime()));
    const minDate = dates.length ? new Date(Math.min(...dates.map(d => d.getTime()))) : new Date();
    const maxDate = dates.length ? new Date(Math.max(...dates.map(d => d.getTime()))) : new Date();

    // Calls over time - daily
    const callsByDate = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const dateCreated = new Date(call.sc_created);
      if (!isNaN(dateCreated.getTime())) {
        const dateKey = dateCreated.toISOString().split('T')[0];
        acc[dateKey] = (acc[dateKey] || 0) + 1;
      }
      return acc;
    }, {});

    // Create a complete time series with all dates
    const timeSeriesData = [];
    for (let d = new Date(minDate); d <= maxDate; d.setDate(d.getDate() + 1)) {
      const dateKey = d.toISOString().split('T')[0];
      timeSeriesData.push({
        date: dateKey,
        count: callsByDate[dateKey] || 0
      });
    }

    // Last 30 days data for heatmap
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const last30DaysData = timeSeriesData
      .filter(d => new Date(d.date) >= thirtyDaysAgo)
      .map(d => ({ date: d.date, value: d.count }));

    // Calls by hour of day
    const callsByHour = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const dateCreated = new Date(call.sc_created);
      if (!isNaN(dateCreated.getTime())) {
        const hour = dateCreated.getHours();
        acc[hour] = (acc[hour] || 0) + 1;
      }
      return acc;
    }, {});

    const hourlyData = Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      count: callsByHour[i] || 0
    }));

    // Calls by day of week
    const callsByDayOfWeek = serviceCalls.reduce((acc: {[key: string]: number}, call) => {
      const dateCreated = new Date(call.sc_created);
      if (!isNaN(dateCreated.getTime())) {
        const dayOfWeek = dateCreated.getDay();
        acc[dayOfWeek] = (acc[dayOfWeek] || 0) + 1;
      }
      return acc;
    }, {});

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const dayOfWeekData = dayNames.map((name, i) => ({
      name,
      value: callsByDayOfWeek[i] || 0
    }));

    // Engineer metrics
    const engineerData = serviceCalls.reduce((acc: {[key: string]: any}, call) => {
      const engineerId = call.uid || 'Unknown';
      if (!acc[engineerId]) {
        acc[engineerId] = {
          id: engineerId,
          name: call.eng_name || engineerId,
          totalCalls: 0,
          openCalls: 0,
          closedCalls: 0,
          responseTime: 0,
          totalResponseTime: 0,
          callsWithResponseTime: 0
        };
      }

      acc[engineerId].totalCalls++;

      if (call.status === 'closed' || call.callStatus === 'closed') {
        acc[engineerId].closedCalls++;
      } else if (call.status === 'open' || call.callStatus === 'open') {
        acc[engineerId].openCalls++;
      }

      // Calculate response time if available
      if (call.inTime && call.outTime) {
        const inTime = new Date(call.inTime);
        const outTime = new Date(call.outTime);
        if (!isNaN(inTime.getTime()) && !isNaN(outTime.getTime())) {
          const responseTime = (outTime.getTime() - inTime.getTime()) / (1000 * 60 * 60); // in hours
          acc[engineerId].totalResponseTime += responseTime;
          acc[engineerId].callsWithResponseTime++;
        }
      }

      return acc;
    }, {});

    // Calculate average response time for each engineer
    Object.values(engineerData).forEach((eng: any) => {
      eng.responseTime = eng.callsWithResponseTime > 0
        ? eng.totalResponseTime / eng.callsWithResponseTime
        : 0;
    });

    const topEngineers = Object.values(engineerData)
      .sort((a: any, b: any) => b.totalCalls - a.totalCalls)
      .slice(0, 10); // Top 10 engineers

    // Prepare radar chart data for top 5 engineers
    const radarData = Object.values(engineerData)
      .sort((a: any, b: any) => b.totalCalls - a.totalCalls)
      .slice(0, 5) // Top 5 engineers
      .map((eng: any) => {
        const completionRate = eng.totalCalls > 0 ? (eng.closedCalls / eng.totalCalls) * 100 : 0;
        return {
          name: eng.name,
          stats: {
            'Total Calls': eng.totalCalls,
            'Completion Rate (%)': completionRate,
            'Response Time (h)': eng.responseTime || 0,
            'Open Calls': eng.openCalls,
            'Closed Calls': eng.closedCalls
          }
        };
      });

    return {
      totalCalls,
      openCalls,
      closedCalls,
      inProgressCalls,
      branchData,
      topBranches,
      typeData,
      ratingData,
      topRatings,
      timeSeriesData,
      last30DaysData,
      hourlyData,
      dayOfWeekData,
      engineerData: Object.values(engineerData),
      topEngineers,
      radarData,
      dateRange: { min: minDate, max: maxDate }
    };
  }, [serviceCalls]);

  // Filter service calls by search query
  const filteredCalls = useMemo(() => {
    if (!searchQuery.trim()) return serviceCalls;

    const lowerQuery = searchQuery.toLowerCase();
    return serviceCalls.filter(call =>
      (call.company && call.company.toLowerCase().includes(lowerQuery)) ||
      (call.branch && call.branch.toLowerCase().includes(lowerQuery)) ||
      (call.eng_remarks && call.eng_remarks.toLowerCase().includes(lowerQuery)) ||
      (call.eng_name && call.eng_name.toLowerCase().includes(lowerQuery)) ||
      (call.sc_id && call.sc_id.toString().includes(lowerQuery))
    );
  }, [serviceCalls, searchQuery]);

  // Handle filter changes
  const handleFilterChange = (newFilters: FilterOptions) => {
    setFilters(newFilters);
  };

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Debug function to check Firebase connection
  const debugFirebaseConnection = async () => {
    setIsLoading(true);
    console.log('Debugging Firebase connection...');

    try {
      const calls = await fetchServiceCalls();
      console.log('Fetched calls directly:', calls.length);
      setServiceCalls(calls);

      if (calls.length === 0) {
        alert('No data found in Firebase. Please check if data was imported correctly.');
      } else {
        alert(`Successfully fetched ${calls.length} service calls from Firebase.`);
      }
    } catch (error) {
      console.error('Error in debug function:', error);
      alert(`Error fetching data: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Set up real-time data subscription
  useEffect(() => {
    let unsubscribe: () => void;

    const setupRealtime = () => {
      console.log('Setting up real-time subscription with filters:', filters);
      unsubscribe = subscribeToServiceCalls((calls) => {
        console.log('Received', calls.length, 'calls from subscription');
        let filteredCalls = calls;

        // Apply current filters to the real-time data
        if (filters.branch) {
          filteredCalls = filteredCalls.filter(call => call.branch === filters.branch);
          console.log('After branch filter:', filteredCalls.length, 'calls');
        }
        if (filters.status) {
          filteredCalls = filteredCalls.filter(call =>
            call.status === filters.status || call.callStatus === filters.status
          );
          console.log('After status filter:', filteredCalls.length, 'calls');
        }
        if (filters.callType) {
          if (filters.callType === 'ASP') {
            filteredCalls = filteredCalls.filter(call => call.is_aspcall);
          } else {
            filteredCalls = filteredCalls.filter(call => call.sc_type === filters.callType);
          }
          console.log('After call type filter:', filteredCalls.length, 'calls');
        }
        if (filters.startDate && filters.endDate) {
          filteredCalls = filteredCalls.filter(call => {
            const callDate = new Date(call.sc_created).toISOString().split('T')[0];
            return callDate >= filters.startDate! && callDate <= filters.endDate!;
          });
          console.log('After date filter:', filteredCalls.length, 'calls');
        }

        setServiceCalls(filteredCalls);
        setIsLoading(false);
      });
    };

    setupRealtime();

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [filters]);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div data-aos="fade-right" data-aos-duration="800">
          <h1 className="text-2xl font-bold">Service Calls Data</h1>
        </div>

        <div data-aos="fade-left" data-aos-duration="800" className="flex items-center gap-2">
          {isLoading && <span className="text-sm text-muted-foreground">Loading...</span>}
          <Button
            variant="outline"
            size="sm"
            onClick={debugFirebaseConnection}
            disabled={isLoading}
          >
            Debug Firebase Connection
          </Button>
        </div>
      </div>

      <div data-aos="fade-down" data-aos-duration="800">
        <FilterBar
          branches={branches}
          statuses={statuses}
          callTypes={callTypes}
          onFilterChange={handleFilterChange}
        />
      </div>

      <div className="mb-6 grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <div data-aos="fade-right" data-aos-delay="100">
          <StatCard
            title="Total Calls"
            value={metrics.totalCalls}
            icon={<Activity className="h-4 w-4" />}
          />
        </div>
        <div data-aos="fade-right" data-aos-delay="200">
          <StatCard
            title="Open Calls"
            value={metrics.openCalls}
            icon={<Clock className="h-4 w-4" />}
            className="border-l-4 border-orange-500"
          />
        </div>
        <div data-aos="fade-left" data-aos-delay="200">
          <StatCard
            title="Closed Calls"
            value={metrics.closedCalls}
            icon={<CheckCircle className="h-4 w-4" />}
            className="border-l-4 border-green-500"
          />
        </div>
        <div data-aos="fade-left" data-aos-delay="100">
          <StatCard
            title="In Progress"
            value={metrics.inProgressCalls}
            icon={<AlertCircle className="h-4 w-4" />}
            className="border-l-4 border-blue-500"
          />
        </div>
      </div>

      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="mb-6" data-aos="fade-up" data-aos-duration="800">
        <TabsList className="grid grid-cols-5 mb-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <span>Trends</span>
          </TabsTrigger>
          <TabsTrigger value="branches" className="flex items-center gap-2">
            <Building className="h-4 w-4" />
            <span>Branches</span>
          </TabsTrigger>
          <TabsTrigger value="engineers" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Engineers</span>
          </TabsTrigger>
          <TabsTrigger value="calls" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            <span>Service Calls</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {/* Summary Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-8">
            <div data-aos="fade-right" data-aos-duration="1000" className="lg:col-span-2">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Call Volume Trend</CardTitle>
                  <CardDescription>Service calls over time</CardDescription>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <CallsTimeSeries data={metrics.timeSeriesData} />
                </CardContent>
              </Card>
            </div>

            <div data-aos="fade-left" data-aos-duration="1000">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Call Types</CardTitle>
                  <CardDescription>Distribution by type</CardDescription>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <DistributionChart
                    title=""
                    description=""
                    data={metrics.typeData}
                    simplified={true}
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Activity and Calendar */}
          <div className="grid gap-6 md:grid-cols-2 mb-8">
            <div data-aos="fade-right" data-aos-duration="1000" data-aos-delay="200">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Recent Activity</CardTitle>
                  <CardDescription>Last 30 days heat map</CardDescription>
                </CardHeader>
                <CardContent className="h-[200px]">
                  <HeatMapChart data={metrics.last30DaysData} />
                </CardContent>
              </Card>
            </div>

            <div data-aos="fade-left" data-aos-duration="1000" data-aos-delay="200">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Day of Week Distribution</CardTitle>
                  <CardDescription>Service calls by day of week</CardDescription>
                </CardHeader>
                <CardContent className="h-[200px]">
                  <DistributionChart
                    title=""
                    description=""
                    data={metrics.dayOfWeekData}
                    simplified={true}
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Top Branches and Ratings */}
          <div className="grid gap-6 md:grid-cols-2 mb-8">
            <div data-aos="fade-right" data-aos-duration="1000" data-aos-delay="300">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Top Branches</CardTitle>
                  <CardDescription>Branches with most service calls</CardDescription>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <DistributionChart
                    title=""
                    description=""
                    data={metrics.topBranches}
                    simplified={true}
                  />
                </CardContent>
              </Card>
            </div>

            <div data-aos="fade-left" data-aos-duration="1000" data-aos-delay="300">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Rating Distribution</CardTitle>
                  <CardDescription>Service calls by rating</CardDescription>
                </CardHeader>
                <CardContent className="h-[300px]">
                  <DistributionChart
                    title=""
                    description=""
                    data={metrics.topRatings}
                    simplified={true}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="trends">
          {/* Time-based Analysis */}
          <div className="grid gap-6 md:grid-cols-2 mb-8">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Hourly Distribution</CardTitle>
                <CardDescription>Service calls by hour of day</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <DistributionChart
                  title=""
                  description=""
                  data={metrics.hourlyData.map(d => ({ name: `${d.hour}:00`, value: d.count }))}
                  simplified={true}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Weekly Pattern</CardTitle>
                <CardDescription>Service calls by day of week</CardDescription>
              </CardHeader>
              <CardContent className="h-[300px]">
                <DistributionChart
                  title=""
                  description=""
                  data={metrics.dayOfWeekData}
                  simplified={true}
                />
              </CardContent>
            </Card>
          </div>

          {/* Calendar View */}
          <Card className="mb-8">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Calendar View</CardTitle>
              <CardDescription>Service calls by date</CardDescription>
            </CardHeader>
            <CardContent>
              <CalendarHeatmap
                data={metrics.timeSeriesData.map(d => ({ date: d.date, count: d.count }))}
              />
            </CardContent>
          </Card>

          {/* Seasonal Analysis */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Activity Heatmap</CardTitle>
              <CardDescription>Service call intensity over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[200px]">
              <HeatMapChart
                data={metrics.timeSeriesData.map(d => ({ date: d.date, value: d.count }))}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="branches">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Branch Performance</CardTitle>
              <CardDescription>Service calls by branch</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex items-center">
                <Input
                  placeholder="Search branches..."
                  className="max-w-sm"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
                <Button variant="outline" className="ml-2" onClick={() => setSearchQuery("")}>
                  Clear
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Branch</TableHead>
                      <TableHead>Total Calls</TableHead>
                      <TableHead>Open Calls</TableHead>
                      <TableHead>Closed Calls</TableHead>
                      <TableHead>Completion Rate</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.branchData.length > 0 ? (
                      metrics.branchData
                        .filter(branch => !searchQuery || branch.name.toLowerCase().includes(searchQuery.toLowerCase()))
                        .map((branch) => {
                          const branchCalls = serviceCalls.filter(call => call.branch === branch.name);
                          const openCalls = branchCalls.filter(call =>
                            call.status === 'open' || call.callStatus === 'open'
                          ).length;
                          const closedCalls = branchCalls.filter(call =>
                            call.status === 'closed' || call.callStatus === 'closed'
                          ).length;
                          const completionRate = branchCalls.length > 0
                            ? (closedCalls / branchCalls.length * 100).toFixed(1)
                            : '0.0';

                          return (
                            <TableRow key={branch.name}>
                              <TableCell>{branch.name}</TableCell>
                              <TableCell>{branch.value}</TableCell>
                              <TableCell>{openCalls}</TableCell>
                              <TableCell>{closedCalls}</TableCell>
                              <TableCell>{completionRate}%</TableCell>
                            </TableRow>
                          );
                        })
                    ) : (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          No branch data found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="engineers">
          {/* Engineer Performance Comparison */}
          <Card className="mb-8">
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Top Engineer Performance Comparison</CardTitle>
              <CardDescription>Multi-dimensional analysis of top 5 engineers</CardDescription>
            </CardHeader>
            <CardContent className="h-[400px]">
              <RadarChart data={metrics.radarData} />
            </CardContent>
          </Card>

          {/* Engineer Table */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Engineer Performance</CardTitle>
              <CardDescription>Service calls by engineer</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex items-center">
                <Input
                  placeholder="Search engineers..."
                  className="max-w-sm"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
                <Button variant="outline" className="ml-2" onClick={() => setSearchQuery("")}>
                  Clear
                </Button>
              </div>

              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Engineer ID</TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Total Calls</TableHead>
                      <TableHead>Open Calls</TableHead>
                      <TableHead>Closed Calls</TableHead>
                      <TableHead>Completion Rate</TableHead>
                      <TableHead>Avg Response Time</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {metrics.engineerData.length > 0 ? (
                      (metrics.engineerData as any[])
                        .filter(eng =>
                          !searchQuery ||
                          eng.id.toString().toLowerCase().includes(searchQuery.toLowerCase()) ||
                          eng.name.toLowerCase().includes(searchQuery.toLowerCase())
                        )
                        .sort((a, b) => b.totalCalls - a.totalCalls)
                        .slice(0, 20) // Show top 20 engineers
                        .map((eng) => {
                          const completionRate = eng.totalCalls > 0
                            ? (eng.closedCalls / eng.totalCalls * 100).toFixed(1)
                            : '0.0';

                          const responseTime = eng.responseTime > 0
                            ? eng.responseTime.toFixed(2)
                            : 'N/A';

                          return (
                            <TableRow key={eng.id}>
                              <TableCell>{eng.id}</TableCell>
                              <TableCell>{eng.name}</TableCell>
                              <TableCell>{eng.totalCalls}</TableCell>
                              <TableCell>{eng.openCalls}</TableCell>
                              <TableCell>{eng.closedCalls}</TableCell>
                              <TableCell>
                                <Badge variant={parseFloat(completionRate) > 75 ? "default" : parseFloat(completionRate) > 50 ? "secondary" : "destructive"}>
                                  {completionRate}%
                                </Badge>
                              </TableCell>
                              <TableCell>{responseTime} {responseTime !== 'N/A' ? 'hrs' : ''}</TableCell>
                            </TableRow>
                          );
                        })
                    ) : (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No engineer data found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calls">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Service Call Logs</CardTitle>
              <CardDescription>Detailed service call records</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4 flex flex-wrap gap-2">
                <div className="flex-1 min-w-[300px]">
                  <Input
                    placeholder="Search calls by company, branch, remarks or engineer..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                  />
                </div>

                <Select
                  value={filters.status || "all"}
                  onValueChange={(value) => handleFilterChange({ ...filters, status: value === "all" ? undefined : value })}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={filters.branch || "all"}
                  onValueChange={(value) => handleFilterChange({ ...filters, branch: value === "all" ? undefined : value })}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Branch" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Branches</SelectItem>
                    {branches.map(branch => (
                      <SelectItem key={branch} value={branch}>{branch}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Button variant="outline" onClick={() => {
                  setSearchQuery("");
                  handleFilterChange({});
                }}>
                  Reset Filters
                </Button>
              </div>

              <div className="flex justify-between items-center mb-4">
                <p className="text-sm text-muted-foreground">
                  {filteredCalls.length} service calls found {searchQuery || Object.keys(filters).length > 0 ? 'matching your filters' : ''}
                </p>

                <div className="flex items-center gap-2">
                  <Badge variant="default" className="flex items-center gap-1 bg-green-500">
                    <CheckCircle className="h-3 w-3" /> {metrics.closedCalls} Closed
                  </Badge>
                  <Badge variant="secondary" className="flex items-center gap-1 bg-yellow-500">
                    <Clock className="h-3 w-3" /> {metrics.openCalls} Open
                  </Badge>
                  <Badge variant="secondary" className="flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" /> {metrics.inProgressCalls} In Progress
                  </Badge>
                </div>
              </div>

              {filteredCalls.length === 0 ? (
                <div className="text-center py-10 text-muted-foreground">
                  No service calls found with the current filters and search criteria.
                </div>
              ) : (
                <div>
                  <CallTable calls={filteredCalls} />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Import missing components
const Table = ({ children }: { children: React.ReactNode }) => (
  <table className="w-full">{children}</table>
);

const TableHeader = ({ children }: { children: React.ReactNode }) => (
  <thead>{children}</thead>
);

const TableBody = ({ children }: { children: React.ReactNode }) => (
  <tbody>{children}</tbody>
);

const TableRow = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <tr className={`border-b ${className}`}>{children}</tr>
);

const TableHead = ({ children, className = "" }: { children: React.ReactNode, className?: string }) => (
  <th className={`h-12 px-4 text-left align-middle font-medium text-muted-foreground ${className}`}>{children}</th>
);

const TableCell = ({ children, className = "", colSpan }: { children: React.ReactNode, className?: string, colSpan?: number }) => (
  <td className={`p-4 align-middle ${className}`} colSpan={colSpan}>{children}</td>
);

export default ServiceCallsData;
