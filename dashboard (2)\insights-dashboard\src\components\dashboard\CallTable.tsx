
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ServiceCall } from "@/services/serviceCallService";
import { formatDate, getStatusColor, truncateText } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { useState } from "react";

interface CallTableProps {
  calls: ServiceCall[];
}

export function CallTable({ calls }: CallTableProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const totalPages = Math.ceil(calls.length / itemsPerPage);
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentCalls = calls.slice(startIndex, endIndex);
  
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date Created</TableHead>
            <TableHead>Branch</TableHead>
            <TableHead>Company</TableHead>
            <TableHead>Rating</TableHead>
            <TableHead>Engineer ID</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Remarks</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {currentCalls.length > 0 ? (
            currentCalls.map((call) => (
              <TableRow key={call.id}>
                <TableCell>{formatDate(call.sc_created)}</TableCell>
                <TableCell>{call.branch}</TableCell>
                <TableCell>{call.company}</TableCell>
                <TableCell>{call.rating}</TableCell>
                <TableCell>{call.uid}</TableCell>
                <TableCell>{call.is_aspcall ? "ASP" : (call.sc_type || "Regular")}</TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <span
                      className={`mr-2 h-2 w-2 rounded-full ${getStatusColor(call.status || call.callStatus || "")}`}
                    ></span>
                    {call.status || call.callStatus}
                  </div>
                </TableCell>
                <TableCell>
                  {truncateText(call.eng_remarks || "", 30)}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={8} className="h-24 text-center">
                No service calls found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
      
      {totalPages > 1 && (
        <div className="flex items-center justify-end space-x-2 p-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="sr-only">Previous page</span>
          </Button>
          <div className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </div>
          <Button
            variant="outline"
            size="icon"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <ChevronRight className="h-4 w-4" />
            <span className="sr-only">Next page</span>
          </Button>
        </div>
      )}
    </div>
  );
}
